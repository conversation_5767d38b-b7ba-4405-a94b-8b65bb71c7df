<?php

header('Content-Type: text/html; charset=utf-8');

// Récupération de l'URL et extraction du slug
$url = $_SERVER['REQUEST_URI'];
$filename = basename($url);
$path_info = pathinfo($filename);
$slug = $path_info['filename'];

// Extraction du code commune (les 5 derniers caractères)
$code_commune = substr($slug, -5);

// Extraction du nom de la ville (tout sauf les 6 derniers caractères : -code)
$ville_slug = substr($slug, 0, -6);
$ville_nom = str_replace('-', ' ', $ville_slug);
$ville_nom = ucwords($ville_nom);

// Paramètres de la base de données
$host = "localhost";
$dbname = "eau";
$user = "root";
$password = "";
$charset = "utf8_general_ci";

// Paramètres de la bdd prod (commentés pour le dev)
// $host = "localhost";
// $dbname = "mnr39x_eau";
// $user = "mnr39x_eau";
// $password = "02;Epn8,%YQx";
// $charset = "utf8_general_ci";

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo "Erreur de connexion à la base de données : " . $e->getMessage();
    die();
}

// Récupération des informations de la ville
$sql_ville = "SELECT * FROM `code_commune` WHERE `COL 1` = :code_commune";
$stmt_ville = $pdo->prepare($sql_ville);
$stmt_ville->bindParam(':code_commune', $code_commune, PDO::PARAM_STR);
$stmt_ville->execute();
$ville_info = $stmt_ville->fetchAll(PDO::FETCH_ASSOC);

if (empty($ville_info)) {
    // Ville non trouvée, redirection vers la page d'accueil
    header('Location: /');
    exit();
}

$ville_nom_db = utf8_encode($ville_info[0]['COL 2']);
$departement = substr($code_commune, 0, 2);

// Fermeture de la connexion
$pdo = null;

?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="/pfas/style-pfas.css">

    <?php
    echo "<title>Analyse PFAS dans l'eau potable à $ville_nom_db ($departement) | LeauPotable.fr</title>";
    echo "<meta name=\"description\" content=\"Résultats de l'analyse des PFAS (substances per- et polyfluoroalkylées) dans l'eau potable à $ville_nom_db ($departement). Données officielles du contrôle sanitaire.\"> ";
    echo "<link rel='canonical' href='https://leaupotable.fr$url'>";
    ?>

</head>
<body>

<?php
include('header.php');
?>

<div class="container">
    <h1 class="text-center bleu">Analyse PFAS dans l'eau potable à <?php echo $ville_nom_db; ?> (<?php echo $departement; ?>)</h1>

    <h2 class="text-center m5">Recherche des substances per- et polyfluoroalkylées (PFAS)</h2>

    <div class="pfas-info-box">
        <h3>Qu'est-ce que les PFAS ?</h3>
        <p>Les PFAS (substances per- et polyfluoroalkylées) sont des composés chimiques synthétiques utilisés dans de nombreux produits industriels et de consommation. Ils sont préoccupants car ils persistent dans l'environnement et peuvent s'accumuler dans l'organisme.</p>
    </div>

    <!-- Zone de recherche et résultats -->
    <div class="pfas-search-container">
        <div class="search-info">
            <p><strong>Commune analysée :</strong> <?php echo $ville_nom_db; ?> (<?php echo $code_commune; ?>)</p>
            <button id="start-pfas-search" class="btn-primary">Lancer l'analyse PFAS</button>
        </div>

        <!-- Barre de progression -->
        <div id="pfas-loading" class="loading-container" style="display: none;">
            <div class="progress-bar">
                <div id="pfas-progress" class="progress-fill"></div>
            </div>
            <p id="pfas-status">Recherche en cours...</p>
        </div>

        <!-- Zone des résultats -->
        <div id="pfas-results" class="results-container">
            <!-- Les résultats seront affichés ici par JavaScript -->
        </div>

        <!-- Compteur de résultats -->
        <div id="results-counter-container" class="results-counter-container" style="display: none;">
            <p id="results-counter" class="results-counter">0 résultat(s) trouvé(s)</p>
        </div>

        <!-- Message si aucun résultat -->
        <div id="pfas-no-results" class="no-results" style="display: none;">
            <h3>Aucune donnée PFAS disponible</h3>
            <p>Aucune analyse PFAS n'a été trouvée pour cette commune dans la base de données officielle. Cela peut signifier :</p>
            <ul>
                <li>Les analyses PFAS n'ont pas encore été effectuées dans cette commune</li>
                <li>Les résultats ne sont pas encore disponibles dans la base de données publique</li>
                <li>Cette commune n'est pas concernée par le programme d'analyse PFAS</li>
            </ul>
        </div>
    </div>

    <!-- Informations complémentaires -->
    <div class="pfas-additional-info">
        <h3>21 paramètres PFAS analysés</h3>
        <p class="pfas-intro">Notre système analyse les 21 paramètres PFAS officiels selon les codes Sandre :</p>
        <div class="pfas-compounds">
            <div class="compound-item">
                <strong>PFOA</strong> - Acide perfluoroctanoïque
            </div>
            <div class="compound-item">
                <strong>PFHpA</strong> - Acide perfluoroheptanoïque
            </div>
            <div class="compound-item">
                <strong>PFHxA</strong> - Acide perfluorohexanoïque
            </div>
            <div class="compound-item">
                <strong>PFPeA</strong> - Acide perfluoropentanoïque
            </div>
            <div class="compound-item">
                <strong>PFBA</strong> - Acide perfluorobutanoïque
            </div>
            <div class="compound-item">
                <strong>PFBS</strong> - Acide perfluorobutane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFDoDA</strong> - Acide perfluorododécanoïque
            </div>
            <div class="compound-item">
                <strong>PFNA</strong> - Acide perfluorononanoique
            </div>
            <div class="compound-item">
                <strong>PFDA</strong> - Acide perfluorodécanoïque
            </div>
            <div class="compound-item">
                <strong>PFUnDA</strong> - Acide perfluoroundécanoïque
            </div>
            <div class="compound-item">
                <strong>PFHpS</strong> - Acide perfluoroheptane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFTrDA</strong> - Acide perfluorotridecanoïque
            </div>
            <div class="compound-item">
                <strong>PFDS</strong> - Acide perfluorodécane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFOS</strong> - Acide perfluorooctane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFHxS</strong> - Acide perfluorohexane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFPeS</strong> - Acide perfluoropentane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFNS</strong> - Acide perfluorononane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFUnDS</strong> - Acide perfluoroundécane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFDoDS</strong> - Acide perfluorododécane sulfonique
            </div>
            <div class="compound-item">
                <strong>PFTrDS</strong> - Acide perfluorotridécane sulfonique
            </div>
            <div class="compound-item special">
                <strong>Somme PFAS</strong> - Somme des 20 PFAS
            </div>
        </div>
    </div>

    <!-- Feedback -->
    <div class="feedback-section">
        <h4 class='text-center bleu m-titre'>Vous avez une suggestion ou constaté un bug ?</h4>
        <p>Partagez vos avis et suggestions pour améliorer notre service gratuit en remplissant le formulaire ci-dessous.</p>
        <?php include('analyse/formulaire-feedback.php'); ?>
    </div>

</div>

<?php include('footer.php'); ?>

<script>
// Configuration pour la recherche PFAS
const COMMUNE_CODE = '<?php echo $code_commune; ?>';
const VILLE_NOM = '<?php echo $ville_nom_db; ?>';

// Codes des paramètres PFAS à analyser (codes Sandre officiels)
const PFAS_PARAMS = [
    { code: "5347", name: "PFOA", fullName: "Acide perfluoroctanoïque" },
    { code: "5977", name: "PFHpA", fullName: "Acide perfluoroheptanoïque" },
    { code: "5978", name: "PFHxA", fullName: "Acide perfluorohexanoïque" },
    { code: "5979", name: "PFPeA", fullName: "Acide perfluoropentanoïque" },
    { code: "5980", name: "PFBA", fullName: "Acide perfluorobutanoïque" },
    { code: "6025", name: "PFBS", fullName: "Acide perfluorobutane sulfonique" },
    { code: "6507", name: "PFDoDA", fullName: "Acide perfluorododécanoïque" },
    { code: "6508", name: "PFNA", fullName: "Acide perfluorononanoique" },
    { code: "6509", name: "PFDA", fullName: "Acide perfluorodécanoïque" },
    { code: "6510", name: "PFUnDA", fullName: "Acide perfluoroundécanoïque" },
    { code: "6542", name: "PFHpS", fullName: "Acide perfluoroheptane sulfonique" },
    { code: "6549", name: "PFTrDA", fullName: "Acide perfluorotridecanoïque" },
    { code: "6550", name: "PFDS", fullName: "Acide perfluorodécane sulfonique" },
    { code: "6561", name: "PFOS", fullName: "Acide perfluorooctane sulfonique" },
    { code: "6830", name: "PFHxS", fullName: "Acide perfluorohexane sulfonique" },
    { code: "8738", name: "PFPeS", fullName: "Acide perfluoropentane sulfonique" },
    { code: "8739", name: "PFNS", fullName: "Acide perfluorononane sulfonique" },
    { code: "8740", name: "PFUnDS", fullName: "Acide perfluoroundécane sulfonique" },
    { code: "8741", name: "PFDoDS", fullName: "Acide perfluorododécane sulfonique" },
    { code: "8742", name: "PFTrDS", fullName: "Acide perfluorotridécane sulfonique" },
    { code: "8847", name: "Somme PFAS", fullName: "Somme des 20 PFAS" }
];
</script>
<script src="/pfas/pfas-search.js"></script>

</body>
</html>
