<!DOCTYPE html>
<html lang="fr">
<head>
    <style>
        .container {
            max-width: 900px;
            margin: auto;
            padding: 10px;
            border: 1px solid #343a40;
            border-radius: 8px;
            text-align: center;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 0.25rem;
            align-items: center;
            justify-content: center;
        }
        .col-md-6, .col-md-12 {
            flex: 1;
            padding: 0.5rem;
           
        }
        .col-md-6 {
            max-width: 50%;
          
        }
        .col-md-12 {
            max-width: 100%;
        }
        label {
            display: block;
            /* width: 50%; */
            /* margin-bottom: 0.5rem; */
            padding-top: 0.5rem; 
            padding-bottom: 0.5rem;
            margin: 0 1rem 0 1rem;
        }
        select{
            width: 60%;
            /* padding: 0.5rem; */
            padding-top: 0.5rem; 
            padding-bottom: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 0 1rem 0 1rem;
            
        }

        .input {
            width: 60%;
            /* padding: 0.5rem; */
            padding-top: 0.5rem; 
            padding-bottom: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 0 1rem 0 1rem;
            
        }

        textarea {
            width: 95%;
            /* padding: 0.5rem; */
            padding-top: 0.5rem; 
            padding-bottom: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 0 1rem 0 1rem;
            
        }

        .btn-success {
            background-color: #74f0a7;
            border: none;
            color: #003250;
            font-weight: 700;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }
        .text-center {
            text-align: center;
        }
        .text-success {
            color: #28a745;
        }
        .mt-4 {
            margin-top: 1.5rem;
        }
        .my-2 {
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .col-md-6 {
                flex: 1 1 100%;
                max-width: 100%;
                margin-bottom: 0.1rem;
                margin-top: 0.1rem;
                
            }


            .row{
                width: 100%;
                /* margin-bottom: 0.1rem; */
            }

            

            input, select, textarea {
            width: 100%;
            
        } 

        }
    </style>
</head>
<body>
    <div class="container">
        <form id="devisForm">
            <!-- <div class="row">
                <div class="col-md-6 my-2">
                    <div class="row">
                        <label for="input-31">Vous êtes&nbsp;&nbsp;</label>
                    <select id="input-31" required>
                        <option value="1">Particulier</option>
                        <option value="2">Professionnel</option>
                        <option value="3">Syndicat de co-propriété</option>
                        <option value="4">Autre</option>
                    </select>
                    </div>
                    
                </div>
                <div class="col-md-6 my-2">
                    <div class="row">
                        <label for="input-412">Situation&nbsp;&nbsp;</label>
                    <select id="input-412" required>
                        <option value="1">Propriétaire / Futur propriétaire</option>
                        <option value="2">Locataire / Futur locataire</option>
                        <option value="3">Administrateur</option>
                        <option value="4">Autre</option>
                    </select> 
                    </div>
                   
                </div>
            </div> -->


            <div class="row">
                <div class="col-md-6 my-2">
                    <div class="row">
                    <label for="input-38">Nom&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
                    <input name="nom" class="input"type="text" id="input-38" required>
                    </div>
                    
                </div>
                <div class="col-md-6 my-2">
                    <div class="row">
                        <label for="input-48">Prénom&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <input name="prenom" class="input"  type="text" id="input-48" required>
                    </div>
                    
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 my-2">
                    <div class="row">
                        <label for="input-3">Email&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <input name="email" class="input"  type="email" id="input-3" required>
                    </div>
                    
                </div>
                <div class="col-md-6 my-2">
                    <div class="row">
                        <label for="input-4">Téléphone&nbsp;&nbsp;</label>
                        <input  class="input" name="tel"  type="tel" id="input-4" required>
                    </div>
                    
                </div>
            </div>
           
            <div class="row">
                <div class="col-md-6 my-2">
                    <div class="row">
                        <label for="input-411">Code postal</label>
                        <input name="cp" class="input"  type="text" id="input-411" required>
                    </div>
                    
                </div>

                <div class="col-md-6 my-2">
                    <div class="row">
                        <label for="input-83">Ville&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <input name="ville" class="input"  type="text" id="input-83" required>
                    </div>
                    
                </div>
            </div>

            <div class="row">

                <div class="col-md-6 my-2">
                   <div class="row">
                       <label for="input-311">Logement</label>
                   <select name="type_bien" id="input-311" required>
                       <option value="1">Appartement</option>
                       <option value="2">Maison</option>
                       <option value="3">Immeuble</option>
                       <option value="4">Bureau</option>
                       <option value="5">Terrain</option>
                       <option value="6">Autre</option>
                   </select>
                   </div>
                   
               </div>

               <div class="col-md-6 my-2">
                   <div class="row">
                       <label name="delais" for="input-41">Projet&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
                       <select id="input-41" required>
                           <option value="1">Adoucisseur d'eau</option>
                           <option value="2">Piscine</option>
                           <option value="3">Récupérateur d'eau de pluie</option>
                           <option value="4">Micro-station d'épuration</option>
                           <option value="4">Autres travaux</option>
                       </select>
                   </div>
                   
               </div>

           </div>

            <!-- <input type="hidden" name="subject" value="adoucisseur"> -->

            
            

            <label class="mt-4" for="textaream1">Description du projet</label>
            <textarea name="description" id="textaream1" rows="6" required></textarea>

            <div class="row">
                <div class="col-md-1 my-2">
                    <input class="input"  type="checkbox" id="chk" required>

                    
                </div>

                <div class="col-md-11 my-2">
                        <label for="chk">J'accepte les Conditions Générales d'Utilisation</label>                    
                </div>
            </div>

            <!-- <div class="my-2">
            </div> -->
            <button type="submit" class="btn-success">Envoyer</button>
        </form>
        <div id="confirmationMessage" class="text-center mt-4 text-success" style="display:none;">
            ✔️ Votre demande a été envoyée avec succès !
        </div>
        <p class="text-center blanc mt-3"><strong>Des devis sans engagement, c'est un service gratuit sans aucune obligation</strong></p>
    </div>
    <script>
        document.getElementById('devisForm').addEventListener('submit', function(event) {
            event.preventDefault();

            const formData = new FormData(this);
            fetch('https://leaupotable.fr/devis.php', {
                method: 'POST',
                body: formData
            }).then(response => {
                if (response.ok) {
                    document.getElementById('confirmationMessage').style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>
