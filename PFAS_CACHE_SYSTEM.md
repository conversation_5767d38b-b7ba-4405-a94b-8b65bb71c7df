# 🚀 Système de Cache PFAS

## 📋 Vue d'ensemble

Le système de cache PFAS permet de sauvegarder automatiquement les résultats d'analyse PFAS pour chaque ville et de les afficher instantanément lors des visites suivantes, améliorant considérablement l'expérience utilisateur.

## 🏗️ Architecture

### 1. **Base de données**
- **Table**: `pfas_cache`
- **Champs**:
  - `id`: Clé primaire auto-incrémentée
  - `commune_code`: Code INSEE de la commune (5 caractères)
  - `commune_name`: Nom de la commune
  - `pfas_data`: Données PFAS au format JSON
  - `created_at`: Date de création
  - `updated_at`: Date de dernière mise à jour
  - `results_count`: Nombre de résultats trouvés

### 2. **API de cache** (`pfas-cache-api.php`)
- **GET** `/pfas-cache-api.php?action=get&code_commune=XXXXX`
  - Récupère les données en cache pour une commune
  - Retourne `{"cached": false}` si aucun cache
  - Retourne les données complètes si cache trouvé

- **POST** `/pfas-cache-api.php?action=save`
  - Sauvegarde les résultats d'analyse dans le cache
  - Met à jour les données existantes ou crée un nouvel enregistrement

### 3. **JavaScript modifié** (`pfas/pfas-search.js`)
- Vérification automatique du cache au démarrage
- Affichage instantané des données en cache
- Sauvegarde automatique après une nouvelle analyse
- Interface utilisateur mise à jour avec indicateur de cache

## 🔄 Flux de fonctionnement

### Premier visiteur sur une ville :
1. ✅ Aucun cache trouvé
2. 🔍 Lancement de l'analyse PFAS complète (21 paramètres)
3. 📊 Affichage progressif des résultats
4. 💾 Sauvegarde automatique dans le cache
5. ✨ Prêt pour les visiteurs suivants

### Visiteurs suivants :
1. ⚡ Cache trouvé
2. 📋 Affichage instantané des données sauvegardées
3. 🔄 Possibilité de relancer l'analyse pour actualiser

## 🎨 Interface utilisateur

### Indicateur de cache
Quand des données en cache sont affichées, un bandeau informatif apparaît :

```
⚡ Données sauvegardées
Dernière analyse effectuée le [date]
Cliquez sur "Relancer l'analyse" pour obtenir des données actualisées
```

### Bouton d'action
- **Première visite**: "Relancer l'analyse PFAS"
- **Avec cache**: "Relancer l'analyse PFAS" (pour actualiser)

## 📁 Fichiers créés/modifiés

### Nouveaux fichiers :
- `pfas-cache-api.php` - API de gestion du cache
- `create_pfas_cache_table.sql` - Script SQL de création de table
- `init-pfas-cache.php` - Script d'initialisation
- `test-pfas-cache.php` - Script de test
- `PFAS_CACHE_SYSTEM.md` - Cette documentation

### Fichiers modifiés :
- `pfas/pfas-search.js` - Logique de cache intégrée
- `pfas/style-pfas.css` - Styles pour l'affichage du cache

## 🚀 Installation

1. **Créer la table de cache** :
   ```bash
   php init-pfas-cache.php
   ```

2. **Vérifier les permissions** :
   - L'API `pfas-cache-api.php` doit être accessible via HTTP
   - La base de données doit être accessible en lecture/écriture

3. **Tester le système** :
   - Aller sur une page PFAS (ex: `/pfas/?ville=Marseille&code=13055`)
   - Lancer une première analyse
   - Recharger la page pour voir le cache en action

## 🔧 Configuration

### Base de données (dans `pfas-cache-api.php`) :
```php
$host = "localhost";
$dbname = "eau";
$user = "root";
$password = "";
```

### Paramètres de cache :
- **Durée de vie** : Illimitée (jusqu'à actualisation manuelle)
- **Taille** : Optimisée avec JSON compressé
- **Index** : Sur `commune_code` et `updated_at`

## 🎯 Avantages

### Pour les utilisateurs :
- ⚡ **Chargement instantané** des données déjà analysées
- 🔄 **Possibilité d'actualisation** à la demande
- 📱 **Expérience fluide** sur mobile et desktop

### Pour le serveur :
- 🚀 **Réduction de charge** sur l'API externe
- 💾 **Moins de requêtes** répétitives
- ⏱️ **Temps de réponse** considérablement amélioré

### Pour la maintenance :
- 📊 **Traçabilité** des analyses effectuées
- 🔍 **Monitoring** possible via la base de données
- 🛠️ **Facilité de débogage** avec logs intégrés

## 🔮 Évolutions possibles

1. **Expiration automatique** : Ajouter une durée de vie aux données
2. **Cache partiel** : Sauvegarder les résultats au fur et à mesure
3. **Compression** : Optimiser le stockage JSON
4. **Statistiques** : Tableau de bord des analyses par ville
5. **API publique** : Exposer les données en cache via API REST

## 🐛 Dépannage

### Cache non fonctionnel :
1. Vérifier que la table `pfas_cache` existe
2. Contrôler les permissions de la base de données
3. Vérifier l'accessibilité de `pfas-cache-api.php`
4. Consulter les logs JavaScript (F12 → Console)

### Données obsolètes :
1. Cliquer sur "Relancer l'analyse" pour actualiser
2. Ou supprimer manuellement l'enregistrement en base

### Erreurs JavaScript :
1. Vérifier la console du navigateur
2. S'assurer que `COMMUNE_CODE` et `VILLE_NOM` sont définis
3. Contrôler la connectivité réseau

---

🎉 **Le système de cache PFAS est maintenant opérationnel !**

Les utilisateurs bénéficient d'une expérience ultra-rapide tout en conservant la possibilité d'obtenir des données actualisées à la demande.
