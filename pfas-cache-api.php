<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration de la base de données
$host = "localhost";
$dbname = "eau";
$user = "root";
$password = "";

// Paramètres de la bdd prod (commentés pour le dev)
// $host = "localhost";
// $dbname = "mnr39x_eau";
// $user = "mnr39x_eau";
// $password = "02;Epn8,%YQx";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Erreur de connexion à la base de données']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if ($action === 'get') {
            getCachedData($pdo);
        } else {
            echo json_encode(['error' => 'Action non valide']);
        }
        break;
        
    case 'POST':
        if ($action === 'save') {
            saveCachedData($pdo);
        } else {
            echo json_encode(['error' => 'Action non valide']);
        }
        break;
        
    default:
        echo json_encode(['error' => 'Méthode non autorisée']);
        break;
}

function getCachedData($pdo) {
    if (!isset($_GET['code_commune'])) {
        echo json_encode(['error' => 'Code commune manquant']);
        return;
    }
    
    $communeCode = $_GET['code_commune'];
    
    try {
        $stmt = $pdo->prepare("
            SELECT commune_name, pfas_data, created_at, updated_at, results_count 
            FROM pfas_cache 
            WHERE commune_code = ? 
            ORDER BY updated_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$communeCode]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $response = [
                'cached' => true,
                'commune_code' => $communeCode,
                'commune_name' => $result['commune_name'],
                'pfas_data' => json_decode($result['pfas_data'], true),
                'created_at' => $result['created_at'],
                'updated_at' => $result['updated_at'],
                'results_count' => $result['results_count']
            ];
        } else {
            $response = [
                'cached' => false,
                'commune_code' => $communeCode
            ];
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur lors de la récupération des données']);
    }
}

function saveCachedData($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['commune_code']) || !isset($input['pfas_data'])) {
        echo json_encode(['error' => 'Données manquantes']);
        return;
    }
    
    $communeCode = $input['commune_code'];
    $communeName = $input['commune_name'] ?? '';
    $pfasData = $input['pfas_data'];
    $resultsCount = $input['results_count'] ?? 0;
    
    try {
        // Vérifier si des données existent déjà
        $stmt = $pdo->prepare("SELECT id FROM pfas_cache WHERE commune_code = ?");
        $stmt->execute([$communeCode]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Mettre à jour les données existantes
            $stmt = $pdo->prepare("
                UPDATE pfas_cache 
                SET commune_name = ?, pfas_data = ?, results_count = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE commune_code = ?
            ");
            $stmt->execute([$communeName, json_encode($pfasData, JSON_UNESCAPED_UNICODE), $resultsCount, $communeCode]);
        } else {
            // Insérer de nouvelles données
            $stmt = $pdo->prepare("
                INSERT INTO pfas_cache (commune_code, commune_name, pfas_data, results_count) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$communeCode, $communeName, json_encode($pfasData, JSON_UNESCAPED_UNICODE), $resultsCount]);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Données sauvegardées avec succès',
            'commune_code' => $communeCode
        ]);
        
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur lors de la sauvegarde des données']);
    }
}
?>
