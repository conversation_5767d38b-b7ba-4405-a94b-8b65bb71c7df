<?php
// Fonctions pour la gestion du cache PFAS côté serveur

/**
 * Récupère les données PFAS en cache pour une commune
 */
function getPFASCache($pdo, $commune_code) {
    try {
        $stmt = $pdo->prepare("
            SELECT commune_name, pfas_data, created_at, updated_at, results_count
            FROM pfas_cache
            WHERE commune_code = ?
            ORDER BY updated_at DESC
            LIMIT 1
        ");
        $stmt->execute([$commune_code]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            return [
                'cached' => true,
                'commune_code' => $commune_code,
                'commune_name' => $result['commune_name'],
                'pfas_data' => json_decode($result['pfas_data'], true),
                'created_at' => $result['created_at'],
                'updated_at' => $result['updated_at'],
                'results_count' => $result['results_count']
            ];
        }

        return ['cached' => false];

    } catch (PDOException $e) {
        error_log("Erreur cache PFAS: " . $e->getMessage());
        return ['cached' => false];
    }
}

/**
 * Affiche le bandeau d'information du cache
 */
function displayCacheInfo($cacheData) {
    $cacheDate = date('d F Y à H:i', strtotime($cacheData['updated_at']));
    $months = [
        'January' => 'janvier', 'February' => 'février', 'March' => 'mars',
        'April' => 'avril', 'May' => 'mai', 'June' => 'juin',
        'July' => 'juillet', 'August' => 'août', 'September' => 'septembre',
        'October' => 'octobre', 'November' => 'novembre', 'December' => 'décembre'
    ];
    $cacheDate = str_replace(array_keys($months), array_values($months), $cacheDate);

    echo '<div class="cache-info">';
    echo '<div class="cache-info-content">';
    echo '<div class="cache-icon">⚡</div>';
    echo '<div class="cache-text">';
    echo '<h4>Données sauvegardées</h4>';
    echo '<p>Dernière analyse effectuée le ' . $cacheDate . '</p>';
    echo '<p class="cache-note">Cliquez sur "Actualiser l\'analyse" pour obtenir des données plus récentes</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Détermine la conformité d'un résultat PFAS
 */
function determineConformity($data) {
    // Vérifier s'il y a au moins une non-conformité
    $hasNonConformity = ($data['conformite_limites_pc_prelevement'] ?? '') === "N" ||
                       ($data['conformite_limites_bact_prelevement'] ?? '') === "N" ||
                       ($data['conformite_references_pc_prelevement'] ?? '') === "N" ||
                       ($data['conformite_references_bact_prelevement'] ?? '') === "N";

    // Vérifier si tout est parfaitement conforme
    $isPerfectlyConform = ($data['conformite_limites_pc_prelevement'] ?? '') === "C" &&
                         ($data['conformite_limites_bact_prelevement'] ?? '') === "C" &&
                         ($data['conformite_references_pc_prelevement'] ?? '') === "C" &&
                         ($data['conformite_references_bact_prelevement'] ?? '') === "C";

    if ($hasNonConformity) {
        return 'non-conform';
    } elseif ($isPerfectlyConform) {
        return 'conform';
    } else {
        return 'neutral';
    }
}

/**
 * Formate le résultat d'analyse pour l'affichage
 */
function getDisplayResult($data) {
    $result = '';

    // Priorité au résultat numérique, puis alphanumérique
    if (!empty($data['resultat_numerique'])) {
        $result = $data['resultat_numerique'];
    } elseif (!empty($data['resultat_alphanumerique'])) {
        $result = $data['resultat_alphanumerique'];
    } else {
        $result = 'Aucune donnée';
    }

    // Ajouter l'unité si disponible et différente de "SANS OBJET"
    if (!empty($data['libelle_unite']) && $data['libelle_unite'] !== "SANS OBJET") {
        $result .= ' ' . $data['libelle_unite'];
    }

    return $result;
}

/**
 * Formate les labels des champs
 */
function formatFieldLabel($fieldName) {
    $labelMap = [
        'libelle_parametre' => 'Paramètre',
        'resultat_numerique' => 'Résultat numérique',
        'resultat_alphanumerique' => 'Résultat alphanumérique',
        'libelle_unite' => 'Unité',
        'limite_qualite_parametre' => 'Limite de qualité',
        'reference_qualite_parametre' => 'Référence de qualité',
        'nom_uge' => 'Unité de gestion',
        'nom_distributeur' => 'Distributeur',
        'nom_moa' => 'Maître d\'ouvrage',
        'date_prelevement' => 'Date de prélèvement',
        'conclusion_conformite_prelevement' => 'Conclusion conformité',
        'conformite_limites_bact_prelevement' => 'Conformité limites bactériologiques',
        'conformite_limites_pc_prelevement' => 'Conformité limites physico-chimiques',
        'conformite_references_bact_prelevement' => 'Conformité références bactériologiques',
        'conformite_references_pc_prelevement' => 'Conformité références physico-chimiques',
        'code_reseau_distribution' => 'Code réseau distribution',
        'nom_reseau_distribution' => 'Nom réseau distribution',
        'code_installation_amont' => 'Code installation amont',
        'nom_installation_amont' => 'Nom installation amont',
        'code_installation_aval' => 'Code installation aval',
        'nom_installation_aval' => 'Nom installation aval',
        'code_prelevement' => 'Code prélèvement',
        'nom_prelevement' => 'Nom prélèvement',
        'code_analyse' => 'Code analyse',
        'reference_analyse' => 'Référence analyse',
        'date_analyse' => 'Date analyse',
        'heure_prelevement' => 'Heure prélèvement',
        'temperature_echantillon' => 'Température échantillon',
        'preleveur' => 'Préleveur',
        'laboratoire' => 'Laboratoire',
        'methode_analyse' => 'Méthode d\'analyse',
        'incertitude_mesure' => 'Incertitude de mesure',
        'limite_detection' => 'Limite de détection',
        'limite_quantification' => 'Limite de quantification'
    ];

    return $labelMap[$fieldName] ?? ucwords(str_replace('_', ' ', $fieldName));
}

/**
 * Formate les valeurs des champs
 */
function formatFieldValue($fieldName, $value) {
    if (strpos($fieldName, 'date') !== false && !empty($value)) {
        return date('d/m/Y', strtotime($value));
    }

    if ($fieldName === 'heure_prelevement' && !empty($value)) {
        return $value;
    }

    if (strpos($fieldName, 'conformite_') !== false && !empty($value)) {
        $conformityMap = [
            'C' => 'Conforme',
            'N' => 'Non conforme',
            'D' => 'Dérogation',
            'S' => 'Sans objet'
        ];
        return $conformityMap[$value] ?? $value;
    }

    return $value ?: 'Non spécifié';
}

/**
 * Affiche un résultat PFAS en cache (reproduction exacte de la fonction JavaScript)
 */
function displayCachedPFASResult($data, $param) {
    // Vérifier s'il y a au moins une non-conformité
    $hasNonConformity = ($data['conformite_limites_pc_prelevement'] ?? '') === "N" ||
                       ($data['conformite_limites_bact_prelevement'] ?? '') === "N" ||
                       ($data['conformite_references_pc_prelevement'] ?? '') === "N" ||
                       ($data['conformite_references_bact_prelevement'] ?? '') === "N";

    // Vérifier si tout est parfaitement conforme
    $isPerfectlyConform = ($data['conformite_limites_pc_prelevement'] ?? '') === "C" &&
                         ($data['conformite_limites_bact_prelevement'] ?? '') === "C" &&
                         ($data['conformite_references_pc_prelevement'] ?? '') === "C" &&
                         ($data['conformite_references_bact_prelevement'] ?? '') === "C";

    // Définir les classes CSS selon la conformité
    if ($hasNonConformity) {
        $conformityClass = 'non-conform';
    } elseif ($isPerfectlyConform) {
        $conformityClass = 'conform';
    } else {
        $conformityClass = 'neutral';
    }

    // Formater la date
    $date = date('d/m/Y', strtotime($data['date_prelevement']));

    // Traitement spécial pour la somme des PFAS
    $isSum = $param['code'] === "8847";
    $headerClass = $isSum ? $conformityClass . ' sum-pfas' : $conformityClass;

    // Champs à ignorer lors de l'affichage
    $fieldsToIgnore = [
        'code_departement', 'nom_departement', 'code_parametre', 'code_parametre_cas',
        'code_parametre_se', 'libelle_parametre_maj', 'libelle_parametre_web',
        'code_type_parametre', 'code_lieu_analyse', 'code_commune', 'nom_commune'
    ];

    // Organiser les détails par sections
    $sections = [
        'Résultats de l\'analyse' => ['limite_detection', 'limite_quantification', 'incertitude_mesure'],
        'Conformité et qualité' => ['limite_qualite_parametre', 'reference_qualite_parametre', 'conclusion_conformite_prelevement', 'conformite_limites_bact_prelevement', 'conformite_limites_pc_prelevement', 'conformite_references_bact_prelevement', 'conformite_references_pc_prelevement'],
        'Prélèvement' => ['heure_prelevement', 'code_prelevement', 'nom_prelevement', 'preleveur', 'temperature_echantillon'],
        'Analyse' => ['date_analyse', 'code_analyse', 'reference_analyse', 'laboratoire', 'methode_analyse'],
        'Réseau et distribution' => ['nom_distributeur', 'nom_uge', 'nom_moa'],
        'Installations' => ['code_installation_amont', 'nom_installation_amont', 'code_installation_aval', 'nom_installation_aval']
    ];

    $allDetailsHTML = '';

    // Afficher les sections organisées
    foreach ($sections as $sectionTitle => $fields) {
        $sectionHTML = '';
        $hasData = false;

        // Traitement spécial pour la section "Réseau et distribution"
        if ($sectionTitle === 'Réseau et distribution') {
            // Afficher les réseaux multiples s'ils existent
            if (!empty($data['all_networks'])) {
                $networksText = is_array($data['all_networks']) ? implode(', ', $data['all_networks']) : (string)$data['all_networks'];
                $sectionHTML .= '
                    <div class="detail-item">
                        <span class="detail-label">Réseaux de distribution :</span>
                        <span class="detail-value">' . htmlspecialchars($networksText) . '</span>
                    </div>
                ';
                $hasData = true;
            }

            // Afficher les autres champs de la section
            foreach ($fields as $field) {
                if (!empty($data[$field])) {
                    $label = formatFieldLabel($field);
                    $formattedValue = formatFieldValue($field, $data[$field]);
                    $sectionHTML .= '
                        <div class="detail-item">
                            <span class="detail-label">' . htmlspecialchars($label) . ' :</span>
                            <span class="detail-value">' . htmlspecialchars($formattedValue) . '</span>
                        </div>
                    ';
                    $hasData = true;
                }
            }
        } else {
            // Traitement normal pour les autres sections
            foreach ($fields as $field) {
                if (!empty($data[$field])) {
                    $label = formatFieldLabel($field);
                    $formattedValue = formatFieldValue($field, $data[$field]);
                    $sectionHTML .= '
                        <div class="detail-item">
                            <span class="detail-label">' . htmlspecialchars($label) . ' :</span>
                            <span class="detail-value">' . htmlspecialchars($formattedValue) . '</span>
                        </div>
                    ';
                    $hasData = true;
                }
            }
        }

        if ($hasData) {
            $allDetailsHTML .= '
                <div class="detail-section">
                    <h5 class="section-title">' . htmlspecialchars($sectionTitle) . '</h5>
                    ' . $sectionHTML . '
                </div>
            ';
        }
    }

    // Champs déjà affichés en haut ou traités spécialement
    $alreadyDisplayedFields = ['libelle_parametre', 'resultat_numerique', 'resultat_alphanumerique', 'libelle_unite', 'date_prelevement', 'all_networks', 'code_reseau_distribution', 'nom_reseau_distribution', 'reseaux'];

    // Ajouter les champs non catégorisés
    $otherFieldsHTML = '';
    $allSectionFields = [];
    foreach ($sections as $sectionFields) {
        $allSectionFields = array_merge($allSectionFields, $sectionFields);
    }

    foreach ($data as $key => $value) {
        if (!in_array($key, $fieldsToIgnore) &&
            !in_array($key, $alreadyDisplayedFields) &&
            !empty($value) &&
            !in_array($key, $allSectionFields)) {
            $label = formatFieldLabel($key);
            $formattedValue = formatFieldValue($key, $value);
            $otherFieldsHTML .= '
                <div class="detail-item">
                    <span class="detail-label">' . htmlspecialchars($label) . ' :</span>
                    <span class="detail-value">' . htmlspecialchars($formattedValue) . '</span>
                </div>
            ';
        }
    }

    if ($otherFieldsHTML) {
        $allDetailsHTML .= '
            <div class="detail-section">
                <h5 class="section-title">Autres informations</h5>
                ' . $otherFieldsHTML . '
            </div>
        ';
    }

    // Afficher le HTML complet
    echo '<div class="pfas-result-item">';
    echo '<div class="pfas-result-header ' . $headerClass . '">';
    echo '<h3>' . htmlspecialchars($param['name']) . ' - ' . htmlspecialchars($param['fullName']) . '</h3>';

    if ($hasNonConformity) {
        echo '<span class="conformity-badge ' . $conformityClass . '">Non conforme</span>';
    } elseif ($isPerfectlyConform) {
        echo '<span class="conformity-badge ' . $conformityClass . '">Conforme</span>';
    }

    echo '</div>';
    echo '<div class="pfas-result-content">';
    echo '<div class="result-main-info">';
    echo '<p class="result-value"><strong>Résultat :</strong> ' . htmlspecialchars(getDisplayResult($data)) . '</p>';
    echo '<p class="result-date"><strong>Date du prélèvement :</strong> ' . $date . '</p>';
    echo '</div>';
    echo '<div class="result-details-complete">';
    echo '<h4>Détails complets de l\'analyse</h4>';
    echo '<div class="all-details">' . $allDetailsHTML . '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Affiche un message "pas de données" pour un paramètre
 */
function displayPFASNoData($param) {
    $isSum = $param['code'] === "8847";
    $headerClass = $isSum ? 'no-data sum-pfas' : 'no-data';

    echo '<div class="pfas-no-data-item cached-result">';
    echo '<div class="pfas-result-header ' . $headerClass . '">';
    echo '<h3>' . htmlspecialchars($param['name']) . ' - ' . htmlspecialchars($param['fullName']) . '</h3>';
    echo '<span class="no-data-badge">Pas de données</span>';
    echo '</div>';
    echo '<div class="pfas-result-content">';
    echo '<div class="no-data-message">';
    echo '<p>Aucune donnée disponible pour ce paramètre dans cette commune.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}
?>
