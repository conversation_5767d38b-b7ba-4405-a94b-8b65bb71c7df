<div class="mar has-text-align-center stickyButton" id="stickyButton">
          <span>
            <label class="btn3" for="modal2">Demander un Devis Gratuit</label>
          </span>
        </div>

    <div class="container-stiky">
        <div class="left-section">
            <h3>Demander un devis gratuit</h3>

            <label class="button-st" for="modal2">Adoucisseur d'eau</label>
            <label class="button-st" for="modal2">Piscine</label>
            <label class="button-st" for="modal2">Récupérateur d'eau de pluie</label>
            <label class="button-st" for="modal2">Micro-station d'épuration</label>
            <label class="button-st" for="modal2">Autres travaux</label>
        </div>
        <div class="right-section">
            <!-- <h1>Right Section</h1>
            <p style="margin-bottom: 1000px;">This is a scrollable right section.</p> -->

            

            <?php
             // Lister les parametres dans un card
include('analyse/display-params.php');

$dep = utf8_encode($dep);

echo"<h4 class='text-center bleu m-titre'>Autres villes du département $dep :</h4>";
include('analyse/autres-villes.php');


    echo"<h5 class='text-center bleu m-titre'>Définition des paramètres analysés pour la qualité de l'eau potable :</h5>
    <div class='text-center'>
    <img src='../qualite-eau-potable.jpg'  title=\"Analyse de la qualité l'eau potable à $villef $code_dep' alt='Qualité l'eau potable à $villef $code_dep\" class='responsive' loading='lazy'> 
    </div>"
;

        echo "<ul>";
            // Affichage des définitions
            foreach ($definition_params as $row) {
                //echo utf8_encode($row['COL 2']) . ":".utf8_encode($row['COL 3']) . "<br>";

                echo "<li><strong>".utf8_encode($row['COL 2']). " : </strong>" .utf8_encode($row['COL 3'])."</li>";
            }
            echo "</ul>";

            
             ?>
        </div>
    </div>

    <!-- Modal structure -->

    <input class="modal-state" id="modal2" type="checkbox" />
        <div class="modal">
          <label class="modal__bg" for="modal2"></label>
          <div class="modal__inner">
            <label class="modal__close" for="modal2"></label>

            <!-- formulaire start -->
            <?php
             include("formDevis.php");
             ?>
            <!-- formulaire end -->

          </div>
        </div>
