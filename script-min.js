document.addEventListener("scroll",(function(){var e=window.scrollY,t=document.getElementById("stickyButton");e>300?t.classList.add("show"):t.classList.remove("show")}));const searchInput=document.getElementById("search"),autocompleteDiv=document.getElementById("autocomplete"),parameterInput=document.getElementById("parameter-search"),parameterAutocompleteDiv=document.getElementById("parameter-autocomplete"),searchButton=document.getElementById("search-button"),resultsDiv=document.getElementById("results"),loadingDiv=document.getElementById("loading");let params=["1345","1302","5900","5902","5901","1399","1398","1447","1340","1339","1449","1303","1841","1382","1369","1335","6276"],allParameters=[],selectedCity=null;const toggleParameterSearchButton=document.getElementById("toggle-parameter-search"),parameterSearchContainer=document.getElementById("parameter-search-container");toggleParameterSearchButton.addEventListener("click",(function(){"none"===parameterSearchContainer.style.display?(parameterSearchContainer.style.display="block",this.textContent="Masquez la recherche de paramètre"):(parameterSearchContainer.style.display="none",this.textContent="Sélectionnez un paramètre spécifique (optionnel)")}));const resetButton=document.getElementById("reset-button");function debounce(e,t){let n;return function(){const a=this,r=arguments;clearTimeout(n),n=setTimeout((()=>e.apply(a,r)),t)}}async function handleCityInput(){const e=searchInput.value;if(e.length<3)autocompleteDiv.style.display="none";else try{displayCitySuggestions(await getCitySuggestions(e))}catch(e){console.error("Erreur lors de la récupération des suggestions:",e)}}async function getCitySuggestions(e){const t=`https://geo.api.gouv.fr/communes?nom=${encodeURIComponent(e)}&fields=nom,code,departement&boost=population&limit=5`,n=await fetch(t);if(!n.ok)throw new Error("Impossible de récupérer les suggestions");return await n.json()}function displayCitySuggestions(e){autocompleteDiv.innerHTML="",0!==e.length?(e.forEach((e=>{const t=document.createElement("div");t.classList.add("suggestion"),t.textContent=`${e.nom} (${e.departement.code})`,t.addEventListener("click",(()=>selectCity(e))),autocompleteDiv.appendChild(t)})),autocompleteDiv.style.display="block"):autocompleteDiv.style.display="none"}function handleParameterInput(){const e=parameterInput.value.toLowerCase();if(e.length<2)return void(parameterAutocompleteDiv.style.display="none");let t=allParameters.filter((t=>t.c.toLowerCase()===e));0===t.length&&(t=allParameters.filter((t=>t.b.toLowerCase().includes(e)))),displayParameterSuggestions(t)}function displayParameterSuggestions(e){parameterAutocompleteDiv.innerHTML="",0!==e.length?(e.forEach((e=>{const t=document.createElement("div");t.classList.add("suggestion"),t.textContent=`${e.c} : ${e.b}`,t.addEventListener("click",(()=>selectParameter(e))),parameterAutocompleteDiv.appendChild(t)})),parameterAutocompleteDiv.style.display="block"):parameterAutocompleteDiv.style.display="none"}function selectParameter(e){parameterInput.value=e.c,parameterAutocompleteDiv.style.display="none",params=[e.a]}function selectCity(e){searchInput.value=e.nom,autocompleteDiv.style.display="none",selectedCity=e}function handleSearch(){selectedCity?getWaterQuality(selectedCity.code):alert("Veuillez sélectionner une ville.")}async function getWaterQuality(e){resultsDiv.innerHTML="",loadingDiv.style.display="block";let t=!1;for(let n=0;n<params.length;n++)try{const a=`https://leaupotable.fr/get-qualite-eau.php?code_commune=${e}&code_parametre=${params[n]}`,r=await fetch(a);if(!r.ok)throw new Error(`Impossible de récupérer les données pour le paramètre ${params[n]}`);const o=await r.json();o.data&&o.data.length>0&&(displayResult(o.data[0]),t=!0),n<params.length-1&&await new Promise((e=>setTimeout(e,2e3)))}catch(e){console.error(`Erreur pour le paramètre ${params[n]}:`,e)}if(loadingDiv.style.display="none",!t){const e=document.createElement("div");e.textContent="Aucun résultat trouvé",e.style.textAlign="center",e.style.marginTop="20px",e.style.fontSize="18px",resultsDiv.appendChild(e)}}function displayResult(e){const t=document.createElement("div");t.classList.add("result-item");const n="C"===e.conformite_references_pc_prelevement&&"C"===e.conformite_references_bact_prelevement&&"C"===e.conformite_limites_pc_prelevement&&"C"===e.conformite_limites_bact_prelevement;t.classList.add(n?"green":"red");const a=e=>"C"===e?"Conforme":"Non conforme";t.innerHTML=`\n          <h3>${e.libelle_parametre}</h3>\n          <p>Résultat numérique : ${e.resultat_numerique}\n          ${"SANS OBJET"!==e.libelle_unite?` ${e.libelle_unite}`:""}\n          </p>\n          <p>Date du prélèvement : ${new Date(e.date_prelevement).toLocaleDateString()}</p>\n          <p>Limite de qualité : ${e.limite_qualite_parametre||"Non spécifiée"}</p>\n          <p>Référence de qualité : ${e.reference_qualite_parametre||"Non spécifiée"}</p>\n          <p>Conclusion conformité prélèvement : ${e.conclusion_conformite_prelevement}</p>\n          <p>Unité de gestion et d'exploitation  : ${e.nom_uge}</p>\n          <p>Nom distributeur : ${e.nom_distributeur}</p>\n          <p>Nom du maître d'ouvrage  : ${e.nom_moa}</p>\n          <p>Conformité références physico-chimiques : ${a(e.conformite_references_pc_prelevement)}</p>\n          <p>Conformité références bactériologiques : ${a(e.conformite_references_bact_prelevement)}</p>\n          <p>Conformité limites physico-chimiques : ${a(e.conformite_limites_pc_prelevement)}</p>\n          <p>Conformité limites bactériologiques : ${a(e.conformite_limites_bact_prelevement)}</p>\n      `,resultsDiv.insertBefore(t,resultsDiv.firstChild)}resetButton.addEventListener("click",(function(){searchInput.value="",selectedCity=null,parameterInput.value="",params=["1345","1302","5900","5902","5901","1399","1398","1447","1340","1339","1449","1303","1841","1382","1369","1335","6276"],autocompleteDiv.style.display="none",parameterAutocompleteDiv.style.display="none",resultsDiv.innerHTML="","block"===parameterSearchContainer.style.display&&(parameterSearchContainer.style.display="none",toggleParameterSearchButton.textContent="Rechercher un paramètre spécifique")})),fetch("params.json").then((e=>e.json())).then((e=>{allParameters=e})).catch((e=>console.error("Erreur lors du chargement des paramètres:",e))),searchInput.addEventListener("input",debounce(handleCityInput,300)),parameterInput.addEventListener("input",debounce(handleParameterInput,300)),searchButton.addEventListener("click",handleSearch);const accordions=document.querySelectorAll(".accordion-header");accordions.forEach((e=>{e.addEventListener("click",(()=>{e.classList.toggle("active");const t=e.nextElementSibling;"block"===t.style.display?t.style.display="none":t.style.display="block"}))}));