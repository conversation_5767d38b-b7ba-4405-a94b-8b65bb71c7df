<?php
// Script d'initialisation de la table de cache PFAS

// Configuration de la base de données
$host = "localhost";
$dbname = "eau";
$user = "root";
$password = "";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Créer la table de cache PFAS
    $sql = "
    CREATE TABLE IF NOT EXISTS pfas_cache (
        id INT AUTO_INCREMENT PRIMARY KEY,
        commune_code VARCHAR(5) NOT NULL,
        commune_name VARCHAR(255) NOT NULL,
        pfas_data JSON NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        results_count INT DEFAULT 0,
        INDEX idx_commune_code (commune_code),
        INDEX idx_updated_at (updated_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
    
    echo "✅ Table pfas_cache créée avec succès !\n";
    echo "📊 Structure de la table :\n";
    echo "- id : Clé primaire auto-incrémentée\n";
    echo "- commune_code : Code INSEE de la commune (5 caractères)\n";
    echo "- commune_name : Nom de la commune\n";
    echo "- pfas_data : Données PFAS au format JSON\n";
    echo "- created_at : Date de création\n";
    echo "- updated_at : Date de dernière mise à jour\n";
    echo "- results_count : Nombre de résultats trouvés\n";
    echo "\n🚀 Le système de cache PFAS est maintenant opérationnel !\n";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la création de la table : " . $e->getMessage() . "\n";
}
?>
