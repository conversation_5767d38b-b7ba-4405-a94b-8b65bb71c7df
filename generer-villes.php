<?php
// Connexion à la base de données
// $servername = "localhost";
// $username = "root";
// $password = "";
// $dbname = "eau";

//bdd prod
$servername = "localhost";
$dbname = "mnr39x_eau";
$username = "mnr39x_eau";
$password = "02;Epn8,%YQx";


try {
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Requête SQL pour récupérer les valeurs distinctes de la colonne 'COL 9' et les valeurs correspondantes de la colonne 'COL 2'
    $sql = "SELECT DISTINCT t1.`COL 9`, t2.`COL 2`
            FROM `totalf` t1
            INNER JOIN code_commune t2 ON t1.`COL 9` = t2.`COL 1`;";

    $result = $pdo->query($sql);

    // Récupération des résultats
    $values = $result->fetchAll(PDO::FETCH_ASSOC);

    /* Générateur de Slug (Friendly Url) : convertit un titre en une URL conviviale.*/
function slugify($text) {
    // Remplacer les caractères spéciaux accentués
    $search = array('À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'à', 'á', 'â', 'ã', 'ä', 'å', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'È', 'É', 'Ê', 'Ë', 'è', 'é', 'ê', 'ë', 'Ç', 'ç', 'Ì', 'Í', 'Î', 'Ï', 'ì', 'í', 'î', 'ï', 'Ù', 'Ú', 'Û', 'Ü', 'ù', 'ú', 'û', 'ü', 'ÿ', 'Ñ', 'ñ');
    $replace = array('A', 'A', 'A', 'A', 'A', 'A', 'a', 'a', 'a', 'a', 'a', 'a', 'O', 'O', 'O', 'O', 'O', 'O', 'o', 'o', 'o', 'o', 'o', 'o', 'E', 'E', 'E', 'E', 'e', 'e', 'e', 'e', 'C', 'c', 'I', 'I', 'I', 'I', 'i', 'i', 'i', 'i', 'U', 'U', 'U', 'U', 'u', 'u', 'u', 'u', 'y', 'N', 'n');

    $text = str_replace($search, $replace, $text);

    // Convertir en minuscules
    $text = strtolower($text);

    // Remplacer les caractères non alphanumériques par des tirets
    $text = preg_replace('/[^a-z0-9-]+/', '-', $text);

    // Supprimer les tirets au début et à la fin
    $text = trim($text, '-');

    return $text;
}
    

    // Affichage des valeurs
    foreach ($values as $row) {

        $valeur2 = utf8_encode($row['COL 2']);
        $slug = slugify($valeur2);
        $valeur9 = $row['COL 9'];

        echo "<a href='/analyse/qualite-eau-potable-$slug-$valeur9' target='_blank'> Qualité de l'eau potable à ". $valeur2 . "-" . $valeur9 . "</a><br><br>";
    }
} catch (PDOException $e) {
    echo "Erreur de connexion à la base de données : " . $e->getMessage();
}

// Fermeture de la connexion
$pdo = null;
?>