document.addEventListener('scroll', function() {
    var scrollPosition = window.scrollY;
    var stickyButton = document.getElementById('stickyButton');

    if (scrollPosition > 300) { // Change 100 to the scroll distance you prefer
      stickyButton.classList.add('show');
    } else {
      stickyButton.classList.remove('show');
    }
  });

  // verifier.php START
  const searchInput = document.getElementById('search');
  const autocompleteDiv = document.getElementById('autocomplete');
  const parameterInput = document.getElementById('parameter-search');
  const parameterAutocompleteDiv = document.getElementById('parameter-autocomplete');
  const searchButton = document.getElementById('search-button');
  const resultsDiv = document.getElementById('results');
  const loadingDiv = document.getElementById('loading');

  let params = ["1345","1302","5900","5902","5901","1399","1398","1447","1340","1339","1449","1303","1841","1382","1369","1335","6276"];
  let allParameters = [];
  let selectedCity = null;

  const toggleParameterSearchButton = document.getElementById('toggle-parameter-search');
const parameterSearchContainer = document.getElementById('parameter-search-container');

toggleParameterSearchButton.addEventListener('click', function() {
if (parameterSearchContainer.style.display === 'none') {
  parameterSearchContainer.style.display = 'block';
  this.textContent = 'Masquez la recherche de paramètre';
} else {
  parameterSearchContainer.style.display = 'none';
  this.textContent = 'Sélectionnez un paramètre spécifique (optionnel)';
}
});

const resetButton = document.getElementById('reset-button');

resetButton.addEventListener('click', function() {
// Réinitialiser le champ de recherche de ville
searchInput.value = '';
selectedCity = null;

// Réinitialiser le champ de recherche de paramètre
parameterInput.value = '';
params = ["1345","1302","5900","5902","5901","1399","1398","1447","1340","1339","1449","1303","1841","1382","1369","1335","6276"];

// Cacher les suggestions
autocompleteDiv.style.display = 'none';
parameterAutocompleteDiv.style.display = 'none';

// Effacer les résultats
resultsDiv.innerHTML = '';

// Si le champ de recherche de paramètre est affiché, le cacher
if (parameterSearchContainer.style.display === 'block') {
  parameterSearchContainer.style.display = 'none';
  toggleParameterSearchButton.textContent = 'Rechercher un paramètre spécifique';
}
});

  // Charger les paramètres depuis le fichier JSON
  fetch('params.json')
      .then(response => response.json())
      .then(data => {
          allParameters = data;
      })
      .catch(error => console.error('Erreur lors du chargement des paramètres:', error));

  searchInput.addEventListener('input', debounce(handleCityInput, 300));
  parameterInput.addEventListener('input', debounce(handleParameterInput, 300));
  searchButton.addEventListener('click', handleSearch);

  function debounce(func, delay) {
      let timeout;
      return function() {
          const context = this;
          const args = arguments;
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(context, args), delay);
      };
  }

  async function handleCityInput() {
      const query = searchInput.value;
      if (query.length < 3) {
          autocompleteDiv.style.display = 'none';
          return;
      }

      try {
          const suggestions = await getCitySuggestions(query);
          displayCitySuggestions(suggestions);
      } catch (error) {
          console.error('Erreur lors de la récupération des suggestions:', error);
      }
  }

  async function getCitySuggestions(query) {
      const apiUrl = `https://geo.api.gouv.fr/communes?nom=${encodeURIComponent(query)}&fields=nom,code,departement&boost=population&limit=5`;
      const response = await fetch(apiUrl);
      if (!response.ok) {
          throw new Error('Impossible de récupérer les suggestions');
      }
      return await response.json();
  }

  function displayCitySuggestions(suggestions) {
      autocompleteDiv.innerHTML = '';
      if (suggestions.length === 0) {
          autocompleteDiv.style.display = 'none';
          return;
      }

      suggestions.forEach(city => {
          const div = document.createElement('div');
          div.classList.add('suggestion');
          div.textContent = `${city.nom} (${city.departement.code})`;
          div.addEventListener('click', () => selectCity(city));
          autocompleteDiv.appendChild(div);
      });

      autocompleteDiv.style.display = 'block';
  }

  function handleParameterInput() {
const query = parameterInput.value.toLowerCase();
if (query.length < 2) {
  parameterAutocompleteDiv.style.display = 'none';
  return;
}

// Recherche d'abord dans c
let suggestions = allParameters.filter(param =>
  param.c.toLowerCase()===query
);

// Si aucun résultat n'est trouvé, recherche dans b
if (suggestions.length === 0) {
  suggestions = allParameters.filter(param =>
      param.b.toLowerCase().includes(query)
  );
}

displayParameterSuggestions(suggestions);
}

  function displayParameterSuggestions(suggestions) {
      parameterAutocompleteDiv.innerHTML = '';
      if (suggestions.length === 0) {
          parameterAutocompleteDiv.style.display = 'none';
          return;
      }

      suggestions.forEach(param => {
          const div = document.createElement('div');
          div.classList.add('suggestion');
          div.textContent = `${param.c} : ${param.b}`;
          div.addEventListener('click', () => selectParameter(param));
          parameterAutocompleteDiv.appendChild(div);
      });

      parameterAutocompleteDiv.style.display = 'block';
  }

  function selectParameter(param) {
      parameterInput.value = param.c;
      parameterAutocompleteDiv.style.display = 'none';
      params = [param.a];
  }

  function selectCity(city) {
      searchInput.value = city.nom;
      autocompleteDiv.style.display = 'none';
      selectedCity = city;
  }

  function handleSearch() {
      if (!selectedCity) {
          alert("Veuillez sélectionner une ville.");
          return;
      }
      getWaterQuality(selectedCity.code);
  }

  async function getWaterQuality(communeCode) {
resultsDiv.innerHTML = '';
loadingDiv.style.display = 'block';

let resultsFound = false;

for (let i = 0; i < params.length; i++) {
  try {
      const apiUrl = `https://leaupotable.fr/get-qualite-eau.php?code_commune=${communeCode}&code_parametre=${params[i]}`;

      const response = await fetch(apiUrl);
      if (!response.ok) {
          throw new Error(`Impossible de récupérer les données pour le paramètre ${params[i]}`);
      }
      const data = await response.json();
      if (data.data && data.data.length > 0) {
          displayResult(data.data[0]);
          resultsFound = true;
      }

      if (i < params.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
      }
  } catch (error) {
      console.error(`Erreur pour le paramètre ${params[i]}:`, error);
  }
}

loadingDiv.style.display = 'none';

if (!resultsFound) {
  const noResultMessage = document.createElement('div');
  noResultMessage.textContent = "Aucun résultat trouvé";
  noResultMessage.style.textAlign = 'center';
  noResultMessage.style.marginTop = '20px';
  noResultMessage.style.fontSize = '18px';
  resultsDiv.appendChild(noResultMessage);
}
}


  function displayResult(item) {
      const resultItem = document.createElement('div');
      resultItem.classList.add('result-item');

      // Vérifier s'il y a au moins une non-conformité pour afficher le badge
      const hasNonConformity = item.conformite_limites_pc_prelevement === "N" ||
                              item.conformite_limites_bact_prelevement === "N" ||
                              item.conformite_references_pc_prelevement === "N" ||
                              item.conformite_references_bact_prelevement === "N";

      // Ajouter la classe de couleur appropriée seulement s'il y a une non-conformité
      if (hasNonConformity) {
          resultItem.classList.add('red');
      }

      // Fonction pour convertir C/N en Conforme/Non conforme
      const getConformiteText = (value) => value === "C" ? "Conforme" : "Non conforme";

      resultItem.innerHTML = `
          <h3>${item.libelle_parametre}</h3>
          <p>Résultat numérique : ${item.resultat_numerique}
          ${item.libelle_unite !== "SANS OBJET" ? ` ${item.libelle_unite}` : ''}
          </p>
          <p>Date du prélèvement : ${new Date(item.date_prelevement).toLocaleDateString()}</p>
          <p>Limite de qualité : ${item.limite_qualite_parametre || 'Non spécifiée'}</p>
          <p>Référence de qualité : ${item.reference_qualite_parametre || 'Non spécifiée'}</p>
          <p>Conclusion conformité prélèvement : ${item.conclusion_conformite_prelevement}</p>
          <p>Unité de gestion et d'exploitation  : ${item.nom_uge}</p>
          <p>Nom distributeur : ${item.nom_distributeur}</p>
          <p>Nom du maître d'ouvrage  : ${item.nom_moa}</p>
          <p>Conformité références physico-chimiques : ${getConformiteText(item.conformite_references_pc_prelevement)}</p>
          <p>Conformité références bactériologiques : ${getConformiteText(item.conformite_references_bact_prelevement)}</p>
          <p>Conformité limites physico-chimiques : ${getConformiteText(item.conformite_limites_pc_prelevement)}</p>
          <p>Conformité limites bactériologiques : ${getConformiteText(item.conformite_limites_bact_prelevement)}</p>
      `;
      resultsDiv.insertBefore(resultItem, resultsDiv.firstChild);
  }


const accordions = document.querySelectorAll('.accordion-header');

accordions.forEach(accordion => {
accordion.addEventListener('click', () => {
  accordion.classList.toggle('active');
  const content = accordion.nextElementSibling;
  if (content.style.display === 'block') {
      content.style.display = 'none';
  } else {
      content.style.display = 'block';
  }
});
});

//verifier.php END