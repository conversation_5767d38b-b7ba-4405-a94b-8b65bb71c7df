<?php

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/Exception.php';
require 'PHPMailer/PHPMailer.php';
require 'PHPMailer/SMTP.php';

if(isset( $_POST['type_bien']))
$type_bien = $_POST['type_bien'];

if(isset( $_POST['type_bien']))
$type_bien = $_POST['type_bien'];

if(isset( $_POST['prenom']))
$prenom = $_POST['prenom'];

if(isset( $_POST['nom']))
$nom = $_POST['nom'];

if(isset( $_POST['email']))
$email = $_POST['email'];

if(isset( $_POST['tel']))
$tel = $_POST['tel'];

if(isset( $_POST['ville']))
$ville = $_POST['ville'];

if(isset( $_POST['cp']))
$cp = $_POST['cp'];

if(isset( $_POST['description']))
$description = $_POST['description'];

if(isset( $_POST['subject']))
$subject = $_POST['subject'];

$content="
    Projet : $subject  <br /> \n
Nom : $nom  <br /> \n
  Prénom : $prenom  <br /> \n
   Email : $email  <br /> \n
    Téléphone : $tel  <br /> \n
     Code postal: $cp  <br /> \n
      Ville : $ville  <br /> \n
      type bien : $type_bien  <br /> \n
       Message : $description";
// $recipient = "<EMAIL>";
// $mailheader = "From: $email \r\n";
// mail($recipient, $subject, $content, $mailheader) or die("Error!");
// echo "Email enyoyé!";

$mail = new PHPMailer(true);

try {
    //Server settings
    $mail->SMTPDebug = 0;                      // Enable verbose debug output
    $mail->isSMTP();                                            // Send using SMTP
    $mail->Host       = 'citron.o2switch.net';                    // Set the SMTP server to send through
    $mail->SMTPAuth   = true;                                   // Enable SMTP authentication
    $mail->Username   = '<EMAIL>';                     // SMTP username
    $mail->Password   = '(V,7n.n@n[gL';                               // SMTP password
    $mail->SMTPSecure = ssl;         // Enable TLS encryption; `PHPMailer::ENCRYPTION_SMTPS` encouraged
    $mail->Port       = 465;                                    // TCP port to connect to, use 465 for `PHPMailer::ENCRYPTION_SMTPS` above

    //Recipients
    $mail->setFrom('<EMAIL>', 'leaupotable.fr');
    $mail->addAddress('<EMAIL>', 'Mounir');     // Add a recipient
    //$mail->addAddress('<EMAIL>');               // Name is optional
    //$mail->addReplyTo( $email , 'Contact maxdevis.fr');


    // Content
    $mail->isHTML(true);                                  // Set email format to HTML
    $mail->type = $type;
    $mail->Body    =  $content;
    //$mail->AltBody = $description;

    $mail->send();
    echo 'description has been sent';
} catch (Exception $e) {
    echo "description could not be sent. Mailer Error: {$mail->ErrorInfo}";
}

    // Redirection vers une page de confirmation
    header("Location: confirmation.php");
    exit();

?>