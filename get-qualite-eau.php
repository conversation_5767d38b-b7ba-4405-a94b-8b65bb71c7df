<?php
$allowedOrigin = 'https://leaupotable.fr';
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

if ($origin !== '' && $origin !== $allowedOrigin) {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

if ($origin) {
    header("Access-Control-Allow-Origin: $origin");
}

header('Content-Type: application/json');
header('Access-Control-Allow-Methods: GET');

$communeCode = $_GET['code_commune'] ?? '';
$paramCode = $_GET['code_parametre'] ?? '';

if (empty($communeCode) || empty($paramCode)) {
    echo json_encode(['error' => 'Missing parameters']);
    exit;
}

// Récupérer tous les champs disponibles et plusieurs résultats pour voir tous les réseaux
$apiUrl = "https://hubeau.eaufrance.fr/api/v1/qualite_eau_potable/resultats_dis?code_commune={$communeCode}&code_parametre={$paramCode}&size=10";

$response = file_get_contents($apiUrl);

if ($response === FALSE) {
    echo json_encode(['error' => 'Unable to fetch data']);
} else {
    echo $response;
}

// header('Content-Type: application/json');
// header('Access-Control-Allow-Origin: *');

// $communeCode = $_GET['code_commune'] ?? '';
// $paramCode = $_GET['code_parametre'] ?? '';

// if (empty($communeCode) || empty($paramCode)) {
//     echo json_encode(['error' => 'Missing parameters']);
//     exit;
// }

// $apiUrl = "https://hubeau.eaufrance.fr/api/v1/qualite_eau_potable/resultats_dis?code_commune={$communeCode}&code_parametre={$paramCode}&fields=libelle_parametre,resultat_numerique,libelle_unite,limite_qualite_parametre,reference_qualite_parametre,nom_uge,nom_distributeur,nom_moa,date_prelevement,conclusion_conformite_prelevement,conformite_limites_bact_prelevement,conformite_limites_pc_prelevement,conformite_references_bact_prelevement,conformite_references_pc_prelevement&size=1";

// $response = file_get_contents($apiUrl);

// if ($response === FALSE) {
//     echo json_encode(['error' => 'Unable to fetch data']);
// } else {
//     echo $response;
// }