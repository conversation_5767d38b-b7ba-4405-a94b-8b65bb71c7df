<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérificateur de Qualité de l'Eau</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .search-container {
            position: relative;
        }
        #search {
            width: 100%;
            padding: 10px;
            font-size: 16px;
        }
        #autocomplete {
            position: absolute;
            width: 100%;
            background-color: white;
            border: 1px solid #ddd;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        .suggestion {
            padding: 10px;
            cursor: pointer;
        }
        .suggestion:hover {
            background-color: #f0f0f0;
        }
        #results {
            margin-top: 20px;
        }
        .result-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .result-item.green {
            background-color: #c8e6c9;  /* Vert clair */
        }
        .result-item.red {
            background-color: #ffcdd2;  /* <PERSON> clair */
        }
        #loading {
            display: none;
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Vérificateur de Qualité de l'Eau</h1>
    <div class="search-container">
        <input type="text" id="search" placeholder="Entrez le nom de la ville">
        <div id="autocomplete"></div>
    </div>
    <div id="loading"></div>
    <div id="results"></div>

    <script>
        const searchInput = document.getElementById('search');
        const autocompleteDiv = document.getElementById('autocomplete');
        const resultsDiv = document.getElementById('results');
        const loadingDiv = document.getElementById('loading');

        const params = ["1345", "1302", "1301", "1303", "5900", "5902", "5901", "1338", "1382", "1447", "1340", "1392", "1339", "1449", "1386", "1337", "1393", "2034", "1751", "1383"];

        searchInput.addEventListener('input', debounce(handleInput, 300));

        function debounce(func, delay) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
        }

        async function handleInput() {
            const query = searchInput.value;
            if (query.length < 3) {
                autocompleteDiv.style.display = 'none';
                return;
            }

            try {
                const suggestions = await getCitySuggestions(query);
                displaySuggestions(suggestions);
            } catch (error) {
                console.error('Erreur lors de la récupération des suggestions:', error);
            }
        }

        async function getCitySuggestions(query) {
            const apiUrl = `https://geo.api.gouv.fr/communes?nom=${encodeURIComponent(query)}&fields=nom,code,departement&boost=population&limit=5`;
            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error('Impossible de récupérer les suggestions');
            }
            return await response.json();
        }

        function displaySuggestions(suggestions) {
            autocompleteDiv.innerHTML = '';
            if (suggestions.length === 0) {
                autocompleteDiv.style.display = 'none';
                return;
            }

            suggestions.forEach(city => {
                const div = document.createElement('div');
                div.classList.add('suggestion');
                div.textContent = `${city.nom} (${city.departement.code})`;
                div.addEventListener('click', () => selectCity(city));
                autocompleteDiv.appendChild(div);
            });

            autocompleteDiv.style.display = 'block';
        }

        function selectCity(city) {
            searchInput.value = city.nom;
            autocompleteDiv.style.display = 'none';
            getWaterQuality(city.code);
        }

        async function getWaterQuality(communeCode) {
            resultsDiv.innerHTML = '';
            loadingDiv.style.display = 'block';

            for (let i = 0; i < params.length; i++) {
                try {
                    const apiUrl = `https://hubeau.eaufrance.fr/api/v1/qualite_eau_potable/resultats_dis?code_commune=${communeCode}&code_parametre=${params[i]}&fields=libelle_parametre%2Cresultat_numerique%2Clibelle_unite%2Climite_qualite_parametre%2Creference_qualite_parametre%2Cnom_uge%2Cnom_distributeur%2Cnom_moa%2Cdate_prelevement%2Cconclusion_conformite_prelevement%2Cconformite_limites_bact_prelevement%2Cconformite_limites_pc_prelevement%2Cconformite_references_bact_prelevement%2Cconformite_references_pc_prelevement&size=1`;
                    
                    const response = await fetch(apiUrl);
                    if (!response.ok) {
                        throw new Error(`Impossible de récupérer les données pour le paramètre ${params[i]}`);
                    }
                    const data = await response.json();
                    if (data.data && data.data.length > 0) {
                        displayResult(data.data[0]);
                    }
                    
                    if (i < params.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                } catch (error) {
                    console.error(`Erreur pour le paramètre ${params[i]}:`, error);
                }
            }
            
            loadingDiv.style.display = 'none';
        }

        function displayResult(item) {
            const resultItem = document.createElement('div');
            resultItem.classList.add('result-item');

            // Vérifier la conformité
            const isConform = item.conformite_references_pc_prelevement === "C" &&
                              item.conformite_references_bact_prelevement === "C" &&
                              item.conformite_limites_pc_prelevement === "C" &&
                              item.conformite_limites_bact_prelevement === "C";

            // Ajouter la classe de couleur appropriée
            resultItem.classList.add(isConform ? 'green' : 'red');

            // Fonction pour convertir C/N en Conforme/Non conforme
            const getConformiteText = (value) => value === "C" ? "Conforme" : "Non conforme";

            resultItem.innerHTML = `
                <h3>${item.libelle_parametre}</h3>
                <p>Résultat numérique : ${item.resultat_numerique}
                ${item.libelle_unite !== "SANS OBJET" ? ` ${item.libelle_unite}` : ''}
                </p>
                <p>Date du prélèvement : ${new Date(item.date_prelevement).toLocaleDateString()}</p>
                <p>Limite de qualité : ${item.limite_qualite_parametre || 'Non spécifiée'}</p>
                <p>Référence de qualité : ${item.reference_qualite_parametre || 'Non spécifiée'}</p>
                <p>Conclusion conformité prélèvement : ${item.conclusion_conformite_prelevement}</p>
                <p>Unité de gestion et d'exploitation  : ${item.nom_uge}</p>
                <p>Nom distributeur : ${item.nom_distributeur}</p>
                <p>Nom du maître d'ouvrage  : ${item.nom_moa}</p>
                <p>Conformité références des paramètres chimiques : ${getConformiteText(item.conformite_references_pc_prelevement)}</p>
                <p>Conformité références bactériologiques : ${getConformiteText(item.conformite_references_bact_prelevement)}</p>
                <p>Conformité limites des paramètres chimiques : ${getConformiteText(item.conformite_limites_pc_prelevement)}</p>
                <p>Conformité limites bactériologiques : ${getConformiteText(item.conformite_limites_bact_prelevement)}</p>
            `;
            resultsDiv.insertBefore(resultItem, resultsDiv.firstChild);
        }
    </script>
</body>
</html>
