// Variables globales
let searchInProgress = false;
let resultsFound = false;
let resultsCount = 0;

// Éléments DOM
const startButton = document.getElementById('start-pfas-search');
const loadingDiv = document.getElementById('pfas-loading');
const progressBar = document.getElementById('pfas-progress');
const statusText = document.getElementById('pfas-status');
const resultsDiv = document.getElementById('pfas-results');
const noResultsDiv = document.getElementById('pfas-no-results');

// Event listener pour le bouton de démarrage
startButton.addEventListener('click', startPFASSearch);

// Fonction principale pour démarrer la recherche PFAS
async function startPFASSearch() {
    if (searchInProgress) return;

    searchInProgress = true;
    resultsFound = false;

    // Réinitialiser l'interface
    resetInterface();

    // Afficher la barre de progression
    showLoading();

    try {
        await searchPFASData();
    } catch (error) {
        console.error('Erreur lors de la recherche PFAS:', error);
        showError('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
    } finally {
        searchInProgress = false;
        hideLoading();

        if (!resultsFound) {
            showNoResults();
        }
    }
}

// Fonction pour rechercher les données PFAS
async function searchPFASData() {
    const totalParams = PFAS_PARAMS.length;
    let currentParam = 0;

    for (const param of PFAS_PARAMS) {
        currentParam++;

        // Mettre à jour la progression
        updateProgress(currentParam, totalParams, `Recherche ${param.name}...`);

        try {
            const apiUrl = `/get-qualite-eau.php?code_commune=${COMMUNE_CODE}&code_parametre=${param.code}`;

            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();

            if (data.data && data.data.length > 0) {
                // Prendre le résultat le plus récent et collecter SEULEMENT ses réseaux
                const latestResult = data.data[0];
                const resultNetworks = collectUniqueNetworks([latestResult]); // Seulement le résultat principal

                // Ajouter les réseaux du résultat principal
                if (resultNetworks.length > 0) {
                    latestResult.all_networks = resultNetworks;
                }

                // Afficher le résultat immédiatement
                displayPFASResult(latestResult, param);
                resultsFound = true;
                resultsCount++;
                updateResultsCounter();
            } else {
                // Afficher un message "pas de données" pour ce paramètre
                displayPFASNoData(param);
            }

            // Pause de 2 secondes entre chaque requête (sauf pour la dernière)
            if (currentParam < totalParams) {
                await delay(2000);
            }

        } catch (error) {
            console.error(`Erreur pour le paramètre ${param.name}:`, error);
            displayPFASError(param, error.message);

            // Pause même en cas d'erreur
            if (currentParam < totalParams) {
                await delay(2000);
            }
        }
    }
}

// Fonction pour afficher un résultat PFAS
function displayPFASResult(data, param) {
    const resultItem = document.createElement('div');
    resultItem.classList.add('pfas-result-item');

    // Vérifier s'il y a au moins une non-conformité pour afficher le badge
    const hasNonConformity = data.conformite_limites_pc_prelevement === "N" ||
                            data.conformite_limites_bact_prelevement === "N" ||
                            data.conformite_references_pc_prelevement === "N" ||
                            data.conformite_references_bact_prelevement === "N";

    // Définir les classes CSS - neutre par défaut, rouge seulement si non conforme
    const conformityClass = hasNonConformity ? 'non-conform' : 'neutral';

    // Formater la date
    const date = new Date(data.date_prelevement).toLocaleDateString('fr-FR');

    // Traitement spécial pour la somme des PFAS
    const isSum = param.code === "8847";
    const headerClass = isSum ? `${conformityClass} sum-pfas` : conformityClass;

    // Champs à ignorer lors de l'affichage
    const fieldsToIgnore = [
        'code_departement', 'nom_departement', 'code_parametre', 'code_parametre_cas',
        'code_parametre_se', 'libelle_parametre_maj', 'libelle_parametre_web',
        'code_type_parametre', 'code_lieu_analyse', 'code_commune', 'nom_commune'
    ];

    // Organiser les détails par sections (en excluant les champs déjà affichés en haut)
    const sections = {
        'Résultats de l\'analyse': ['limite_detection', 'limite_quantification', 'incertitude_mesure'],
        'Conformité et qualité': ['limite_qualite_parametre', 'reference_qualite_parametre', 'conclusion_conformite_prelevement', 'conformite_limites_bact_prelevement', 'conformite_limites_pc_prelevement', 'conformite_references_bact_prelevement', 'conformite_references_pc_prelevement'],
        'Prélèvement': ['heure_prelevement', 'code_prelevement', 'nom_prelevement', 'preleveur', 'temperature_echantillon'],
        'Analyse': ['date_analyse', 'code_analyse', 'reference_analyse', 'laboratoire', 'methode_analyse'],
        'Réseau et distribution': ['nom_distributeur', 'nom_uge', 'nom_moa'],
        'Installations': ['code_installation_amont', 'nom_installation_amont', 'code_installation_aval', 'nom_installation_aval']
    };

    let allDetailsHTML = '';

    // Afficher les sections organisées
    for (const [sectionTitle, fields] of Object.entries(sections)) {
        let sectionHTML = '';
        let hasData = false;

        // Traitement spécial pour la section "Réseau et distribution"
        if (sectionTitle === 'Réseau et distribution') {
            // Afficher les réseaux multiples s'ils existent
            if (data.all_networks && data.all_networks.length > 0) {
                const networksText = Array.isArray(data.all_networks) ? data.all_networks.join(', ') : String(data.all_networks);
                sectionHTML += `
                    <div class="detail-item">
                        <span class="detail-label">Réseaux de distribution :</span>
                        <span class="detail-value">${networksText}</span>
                    </div>
                `;
                hasData = true;
            }

            // Afficher les autres champs de la section
            for (const field of fields) {
                if (data[field] !== null && data[field] !== '' && data[field] !== undefined) {
                    const label = formatFieldLabel(field);
                    const formattedValue = formatFieldValue(field, data[field]);
                    sectionHTML += `
                        <div class="detail-item">
                            <span class="detail-label">${label} :</span>
                            <span class="detail-value">${formattedValue}</span>
                        </div>
                    `;
                    hasData = true;
                }
            }
        } else {
            // Traitement normal pour les autres sections
            for (const field of fields) {
                if (data[field] !== null && data[field] !== '' && data[field] !== undefined) {
                    const label = formatFieldLabel(field);
                    const formattedValue = formatFieldValue(field, data[field]);
                    sectionHTML += `
                        <div class="detail-item">
                            <span class="detail-label">${label} :</span>
                            <span class="detail-value">${formattedValue}</span>
                        </div>
                    `;
                    hasData = true;
                }
            }
        }

        if (hasData) {
            allDetailsHTML += `
                <div class="detail-section">
                    <h5 class="section-title">${sectionTitle}</h5>
                    ${sectionHTML}
                </div>
            `;
        }
    }

    // Champs déjà affichés en haut ou traités spécialement (à exclure des sections détaillées)
    const alreadyDisplayedFields = ['libelle_parametre', 'resultat_numerique', 'resultat_alphanumerique', 'libelle_unite', 'date_prelevement', 'all_networks', 'code_reseau_distribution', 'nom_reseau_distribution', 'reseaux'];

    // Ajouter les champs non catégorisés
    let otherFieldsHTML = '';
    for (const [key, value] of Object.entries(data)) {
        if (!fieldsToIgnore.includes(key) &&
            !alreadyDisplayedFields.includes(key) &&
            value !== null && value !== '' &&
            !Object.values(sections).flat().includes(key)) {
            const label = formatFieldLabel(key);
            const formattedValue = formatFieldValue(key, value);
            otherFieldsHTML += `
                <div class="detail-item">
                    <span class="detail-label">${label} :</span>
                    <span class="detail-value">${formattedValue}</span>
                </div>
            `;
        }
    }

    if (otherFieldsHTML) {
        allDetailsHTML += `
            <div class="detail-section">
                <h5 class="section-title">Autres informations</h5>
                ${otherFieldsHTML}
            </div>
        `;
    }

    // Créer le contenu HTML
    resultItem.innerHTML = `
        <div class="pfas-result-header ${headerClass}">
            <h3>${param.name} - ${param.fullName}</h3>
            ${hasNonConformity ? `<span class="conformity-badge ${conformityClass}">Non conforme</span>` : ''}
        </div>
        <div class="pfas-result-content">
            <div class="result-main-info">
                <p class="result-value">
                    <strong>Résultat :</strong>
                    ${getDisplayResult(data)}
                </p>
                <p class="result-date">
                    <strong>Date du prélèvement :</strong> ${date}
                </p>
            </div>
            <div class="result-details-complete">
                <h4>Détails complets de l'analyse</h4>
                <div class="all-details">
                    ${allDetailsHTML}
                </div>
            </div>
        </div>
    `;

    resultsDiv.appendChild(resultItem);

    // Faire défiler vers le nouveau résultat
    resultItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Fonction pour obtenir le résultat à afficher (sans "Non détecté")
function getDisplayResult(data) {
    let result = '';

    // Priorité au résultat numérique, puis alphanumérique
    if (data.resultat_numerique !== null && data.resultat_numerique !== undefined && data.resultat_numerique !== '') {
        result = data.resultat_numerique;
    } else if (data.resultat_alphanumerique !== null && data.resultat_alphanumerique !== undefined && data.resultat_alphanumerique !== '') {
        result = data.resultat_alphanumerique;
    } else {
        result = 'Aucune donnée';
    }

    // Ajouter l'unité si disponible et différente de "SANS OBJET"
    if (data.libelle_unite && data.libelle_unite !== "SANS OBJET" && data.libelle_unite !== "") {
        result += ` ${data.libelle_unite}`;
    }

    return result;
}

// Fonction pour formater les labels des champs
function formatFieldLabel(fieldName) {
    const labelMap = {
        'libelle_parametre': 'Paramètre',
        'resultat_numerique': 'Résultat numérique',
        'resultat_alphanumerique': 'Résultat alphanumérique',
        'libelle_unite': 'Unité',
        'limite_qualite_parametre': 'Limite de qualité',
        'reference_qualite_parametre': 'Référence de qualité',
        'nom_uge': 'Unité de gestion',
        'nom_distributeur': 'Distributeur',
        'nom_moa': 'Maître d\'ouvrage',
        'date_prelevement': 'Date de prélèvement',
        'conclusion_conformite_prelevement': 'Conclusion conformité',
        'conformite_limites_bact_prelevement': 'Conformité limites bactériologiques',
        'conformite_limites_pc_prelevement': 'Conformité limites physico-chimiques',
        'conformite_references_bact_prelevement': 'Conformité références bactériologiques',
        'conformite_references_pc_prelevement': 'Conformité références physico-chimiques',
        'code_reseau_distribution': 'Code réseau distribution',
        'nom_reseau_distribution': 'Nom réseau distribution',
        'code_installation_amont': 'Code installation amont',
        'nom_installation_amont': 'Nom installation amont',
        'code_installation_aval': 'Code installation aval',
        'nom_installation_aval': 'Nom installation aval',
        'code_prelevement': 'Code prélèvement',
        'nom_prelevement': 'Nom prélèvement',
        'code_analyse': 'Code analyse',
        'reference_analyse': 'Référence analyse',
        'date_analyse': 'Date analyse',
        'heure_prelevement': 'Heure prélèvement',
        'temperature_echantillon': 'Température échantillon',
        'preleveur': 'Préleveur',
        'laboratoire': 'Laboratoire',
        'methode_analyse': 'Méthode d\'analyse',
        'incertitude_mesure': 'Incertitude de mesure',
        'limite_detection': 'Limite de détection',
        'limite_quantification': 'Limite de quantification'
    };

    return labelMap[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// Fonction pour formater les valeurs des champs
function formatFieldValue(fieldName, value) {
    if (fieldName.includes('date') && value) {
        return new Date(value).toLocaleDateString('fr-FR');
    }

    if (fieldName === 'heure_prelevement' && value) {
        return value;
    }

    if (fieldName.includes('conformite_') && value) {
        const conformityMap = {
            'C': 'Conforme',
            'N': 'Non conforme',
            'D': 'Dérogation',
            'S': 'Sans objet'
        };
        return conformityMap[value] || value;
    }

    return value || 'Non spécifié';
}

// Fonction pour afficher un message "pas de données"
function displayPFASNoData(param) {
    const noDataItem = document.createElement('div');
    noDataItem.classList.add('pfas-no-data-item');

    // Traitement spécial pour la somme des PFAS
    const isSum = param.code === "8847";
    const headerClass = isSum ? 'no-data sum-pfas' : 'no-data';

    noDataItem.innerHTML = `
        <div class="pfas-result-header ${headerClass}">
            <h3>${param.name} - ${param.fullName}</h3>
            <span class="no-data-badge">Pas de données</span>
        </div>
        <div class="pfas-result-content">
            <div class="no-data-message">
                <p>Aucune donnée disponible pour ce paramètre dans cette commune.</p>
                <p class="no-data-explanation">
                    Cela peut signifier que ce paramètre n'a pas encore été analysé ou que les résultats ne sont pas disponibles dans la base de données publique.
                </p>
            </div>
        </div>
    `;

    resultsDiv.appendChild(noDataItem);

    // Faire défiler vers le nouveau résultat
    noDataItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Fonction pour afficher une erreur pour un paramètre spécifique
function displayPFASError(param, errorMessage) {
    const errorItem = document.createElement('div');
    errorItem.classList.add('pfas-error-item');
    errorItem.setAttribute('data-param-code', param.code);

    errorItem.innerHTML = `
        <div class="pfas-error-header">
            <h3>${param.name} - ${param.fullName}</h3>
            <span class="error-badge">Erreur</span>
        </div>
        <div class="pfas-error-content">
            <p>Impossible de récupérer les données pour ce paramètre.</p>
            <p class="error-details">${errorMessage}</p>
            <div class="error-actions">
                <button class="retry-button" onclick="retryParameter('${param.code}', '${param.name}', '${param.fullName}')">
                    Relancer le test pour ${param.name}
                </button>
            </div>
        </div>
    `;

    resultsDiv.appendChild(errorItem);

    // Faire défiler vers le nouveau résultat
    errorItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Fonction pour déterminer la conformité
function determineConformity(data) {
    // Vérifier tous les champs de conformité
    // Retourner false (non conforme) si au moins un champ est "N" (Non conforme)
    return !(data.conformite_limites_pc_prelevement === "N" ||
             data.conformite_limites_bact_prelevement === "N" ||
             data.conformite_references_pc_prelevement === "N" ||
             data.conformite_references_bact_prelevement === "N");
}

// Fonctions utilitaires pour l'interface
function resetInterface() {
    resultsDiv.innerHTML = '';
    noResultsDiv.style.display = 'none';
    startButton.disabled = true;
    startButton.textContent = 'Recherche en cours...';
    resultsCount = 0;
    updateResultsCounter();
}

function showLoading() {
    loadingDiv.style.display = 'block';
    document.getElementById('results-counter-container').style.display = 'block';
    updateProgress(0, PFAS_PARAMS.length, 'Initialisation...');
}

function hideLoading() {
    loadingDiv.style.display = 'none';
    startButton.disabled = false;
    startButton.textContent = 'Relancer l\'analyse PFAS';
}

function updateProgress(current, total, status) {
    const percentage = (current / total) * 100;
    progressBar.style.width = `${percentage}%`;
    statusText.textContent = status;
}

function showNoResults() {
    noResultsDiv.style.display = 'block';
}

function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.classList.add('error-message');
    errorDiv.innerHTML = `
        <h3>Erreur</h3>
        <p>${message}</p>
    `;
    resultsDiv.appendChild(errorDiv);
}

// Fonction utilitaire pour créer un délai
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Fonction pour mettre à jour le compteur de résultats
function updateResultsCounter() {
    const counterElement = document.getElementById('results-counter');
    if (counterElement) {
        counterElement.textContent = `${resultsCount} résultat(s) trouvé(s)`;
    }
}

// Fonction pour collecter tous les réseaux uniques
function collectUniqueNetworks(dataArray) {
    const networks = new Set();

    dataArray.forEach(item => {
        // Vérifier le champ nom_reseau_distribution (ancien format)
        if (item.nom_reseau_distribution && item.nom_reseau_distribution.trim() !== '') {
            networks.add(item.nom_reseau_distribution.trim());
        }

        // Vérifier le champ reseaux (nouveau format avec array d'objets)
        if (item.reseaux && Array.isArray(item.reseaux)) {
            item.reseaux.forEach(reseau => {
                if (reseau.nom && reseau.nom.trim() !== '') {
                    let networkName = reseau.nom.trim();
                    // Ajouter le débit si disponible
                    if (reseau.debit && reseau.debit !== '100 %') {
                        networkName += ` (${reseau.debit})`;
                    }
                    networks.add(networkName);
                }
            });
        }
    });

    return Array.from(networks);
}

// Fonction pour relancer un paramètre spécifique
async function retryParameter(paramCode, paramName, paramFullName) {
    const param = { code: paramCode, name: paramName, fullName: paramFullName };

    // Trouver et supprimer l'élément d'erreur existant
    const existingError = document.querySelector(`[data-param-code="${paramCode}"]`);
    if (existingError) {
        existingError.remove();
    }

    // Créer un élément de chargement temporaire
    const loadingItem = document.createElement('div');
    loadingItem.classList.add('pfas-retry-loading');
    loadingItem.setAttribute('data-param-code', paramCode);

    loadingItem.innerHTML = `
        <div class="pfas-result-header">
            <h3>${paramName} - ${paramFullName}</h3>
            <span class="loading-badge">Rechargement...</span>
        </div>
        <div class="pfas-result-content">
            <div class="retry-loading-message">
                <p>Nouvelle tentative de récupération des données...</p>
            </div>
        </div>
    `;

    resultsDiv.appendChild(loadingItem);
    loadingItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

    try {
        const apiUrl = `/get-qualite-eau.php?code_commune=${COMMUNE_CODE}&code_parametre=${paramCode}`;

        const response = await fetch(apiUrl);
        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }

        const data = await response.json();

        // Supprimer l'élément de chargement
        loadingItem.remove();

        if (data.data && data.data.length > 0) {
            // Prendre le résultat le plus récent et collecter SEULEMENT ses réseaux
            const latestResult = data.data[0];
            const resultNetworks = collectUniqueNetworks([latestResult]); // Seulement le résultat principal

            // Ajouter les réseaux du résultat principal
            if (resultNetworks.length > 0) {
                latestResult.all_networks = resultNetworks;
            }

            // Afficher le résultat
            displayPFASResult(latestResult, param);
            resultsCount++;
            updateResultsCounter();
        } else {
            // Afficher un message "pas de données"
            displayPFASNoData(param);
        }

    } catch (error) {
        console.error(`Erreur lors de la nouvelle tentative pour ${paramName}:`, error);

        // Supprimer l'élément de chargement
        loadingItem.remove();

        // Afficher l'erreur à nouveau
        displayPFASError(param, error.message);
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    console.log(`Page PFAS initialisée pour ${VILLE_NOM} (${COMMUNE_CODE})`);
    console.log(`${PFAS_PARAMS.length} paramètres PFAS à analyser`);
});
