<?php
// Script de test pour le système de cache SEO PFAS

echo "🧪 Test du système de cache SEO PFAS\n\n";

// Configuration de la base de données
$host = "localhost";
$dbname = "eau";
$user = "root";
$password = "";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo "❌ Erreur de connexion à la base de données : " . $e->getMessage() . "\n";
    exit;
}

// Créer des données de test pour Marseille
echo "1️⃣ Création de données de test pour Marseille (13055)...\n";

$testData = [
    '5347' => [
        'param_name' => 'PFOA',
        'param_code' => '5347',
        'data' => [
            'libelle_parametre' => 'Acide perfluoroctanoïque',
            'resultat_numerique' => '0.008',
            'libelle_unite' => 'µg/L',
            'date_prelevement' => '2024-01-15',
            'conformite_limites_pc_prelevement' => 'C',
            'conformite_limites_bact_prelevement' => 'S',
            'conformite_references_pc_prelevement' => 'C',
            'conformite_references_bact_prelevement' => 'S',
            'nom_distributeur' => 'Métropole Aix-Marseille-Provence',
            'laboratoire' => 'Laboratoire départemental d\'analyses'
        ]
    ],
    '6561' => [
        'param_name' => 'PFOS',
        'param_code' => '6561',
        'data' => [
            'libelle_parametre' => 'Acide perfluorooctane sulfonique',
            'resultat_numerique' => '0.003',
            'libelle_unite' => 'µg/L',
            'date_prelevement' => '2024-01-15',
            'conformite_limites_pc_prelevement' => 'C',
            'conformite_limites_bact_prelevement' => 'S',
            'conformite_references_pc_prelevement' => 'C',
            'conformite_references_bact_prelevement' => 'S',
            'nom_distributeur' => 'Métropole Aix-Marseille-Provence',
            'laboratoire' => 'Laboratoire départemental d\'analyses'
        ]
    ],
    '8847' => [
        'param_name' => 'Somme PFAS',
        'param_code' => '8847',
        'data' => [
            'libelle_parametre' => 'Somme des 20 PFAS',
            'resultat_numerique' => '0.011',
            'libelle_unite' => 'µg/L',
            'date_prelevement' => '2024-01-15',
            'conformite_limites_pc_prelevement' => 'C',
            'conformite_limites_bact_prelevement' => 'S',
            'conformite_references_pc_prelevement' => 'C',
            'conformite_references_bact_prelevement' => 'S',
            'nom_distributeur' => 'Métropole Aix-Marseille-Provence',
            'laboratoire' => 'Laboratoire départemental d\'analyses'
        ]
    ],
    '5977' => [
        'param_name' => 'PFHpA',
        'param_code' => '5977',
        'data' => null // Pas de données pour ce paramètre
    ]
];

// Insérer les données de test
try {
    // Supprimer les données existantes pour Marseille
    $stmt = $pdo->prepare("DELETE FROM pfas_cache WHERE commune_code = ?");
    $stmt->execute(['13055']);
    
    // Insérer les nouvelles données
    $stmt = $pdo->prepare("
        INSERT INTO pfas_cache (commune_code, commune_name, pfas_data, results_count) 
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([
        '13055',
        'Marseille',
        json_encode($testData, JSON_UNESCAPED_UNICODE),
        3 // 3 résultats trouvés
    ]);
    
    echo "✅ Données de test créées avec succès\n";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de l'insertion des données : " . $e->getMessage() . "\n";
    exit;
}

// Test de récupération
echo "\n2️⃣ Test de récupération des données...\n";

include('pfas/pfas-cache-functions.php');
$cacheData = getPFASCache($pdo, '13055');

if ($cacheData['cached']) {
    echo "✅ Cache récupéré avec succès\n";
    echo "📊 Commune: " . $cacheData['commune_name'] . "\n";
    echo "📊 Nombre de résultats: " . $cacheData['results_count'] . "\n";
    echo "📊 Nombre de paramètres en cache: " . count($cacheData['pfas_data']) . "\n";
} else {
    echo "❌ Erreur lors de la récupération du cache\n";
}

echo "\n3️⃣ Test de l'affichage HTML...\n";

// Simuler l'affichage d'un résultat
$param = ['code' => '5347', 'name' => 'PFOA', 'fullName' => 'Acide perfluoroctanoïque'];
$data = $testData['5347']['data'];

echo "✅ Test d'affichage d'un résultat PFAS :\n";
echo "- Paramètre: " . $param['name'] . "\n";
echo "- Résultat: " . getDisplayResult($data) . "\n";
echo "- Conformité: " . determineConformity($data) . "\n";

echo "\n🎉 Tests terminés avec succès !\n";
echo "\n💡 Pour tester le rendu SEO :\n";
echo "1. Allez sur : http://eau.test/pfas/marseille-13055\n";
echo "2. Vérifiez que les données s'affichent immédiatement (côté serveur)\n";
echo "3. Inspectez le code source - les données doivent être visibles\n";
echo "4. Cliquez sur 'Actualiser l'analyse' pour tester la mise à jour\n";
echo "\n🔍 Pour tester le SEO :\n";
echo "- Désactivez JavaScript et rechargez la page\n";
echo "- Les données doivent toujours être visibles\n";
echo "- Utilisez un crawler/bot pour vérifier l'indexation\n";

$pdo = null;
?>
