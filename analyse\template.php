<?php

header('Content-Type: text/html; charset=utf-8');

// URL
//$url = "https://site.com/ville-01002.php";

$url = $_SERVER['REQUEST_URI'];

// Obtenez le nom du fichier à partir de l'URL
$filename = basename($url);

// Obtenez les informations sur le chemin du fichier
$path_info = pathinfo($filename);

// Extrayez le slug de la clé "filename" dans le tableau path_info
$slug = $path_info['filename'];

// Récupérer le numéro à partir du slug (supposons que le numéro est toujours à la fin)
$variable = substr($slug, -5); // -5 pour extraire les 5 derniers caractères

//$cd = substr($slug, 5); // -5 pour extraire les 5 derniers caractères


// Afficher le résultat
//echo $variable;


// Paramètres de la base de données
$host = "localhost";
$dbname = "eau";
$user = "root";
$password = "";
$charset = "utf8_general_ci"; // Utilisez le jeu de caractères approprié

// Paramètres de la bdd prod
// $host = "localhost";
// $dbname = "mnr39x_eau";
// $user = "mnr39x_eau";
// $password = "02;Epn8,%YQx";
// $charset = "utf8_general_ci";

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    //echo "Connexion réussie à la base de données\n";
} catch (PDOException $e) {
    echo "Erreur de connexion à la base de données : " . $e->getMessage();
    die();
}

// Variable pour la requête
// $variable = 75056;

// Requête SQL avec une variable
$sql = "SELECT * FROM `totalf2` WHERE `COL 9` = :variable";

// Préparation de la requête
$stmt = $pdo->prepare($sql);

// Liaison de la variable
$stmt->bindParam(':variable', $variable, PDO::PARAM_STR);

// Exécution de la requête
$stmt->execute();

// Récupération des résultats
$resultats = $stmt->fetchAll(PDO::FETCH_ASSOC);


// Effectuer une deuxième requête pour récupérer des données d'une autre table
$requeteTable2 = "SELECT * FROM `code_commune` WHERE `COL 1` = :variable";
$statementTable2 = $pdo->prepare($requeteTable2);
$statementTable2->bindParam(':variable', $variable, PDO::PARAM_STR); // Remplacez $votreVariable par la valeur réelle
$statementTable2->execute();
$ville = $statementTable2->fetchAll(PDO::FETCH_ASSOC);

//print_r($ville);

// Effectuer une 3eme requête pour récupérer des données d'une autre table
$code_dep = $resultats[0]['COL 1'];

//echo $code_dep;

$requeteTable3 = "SELECT * FROM `code_commune` WHERE `COL 1` LIKE :variable LIMIT 10";
$statementTable3 = $pdo->prepare($requeteTable3);

// Ajouter le caractère joker "%" pour correspondre à toutes les valeurs commençant par 95
$code_dep2 = $code_dep . "%";

//echo($code_dep2);

$statementTable3->bindParam(':variable', $code_dep2, PDO::PARAM_STR); // Remplacez $votreVariable par la valeur réelle
$statementTable3->execute();
$villes_dep = $statementTable3->fetchAll(PDO::FETCH_ASSOC);

// Effectuer une 4eme requête pour récupérer des définitions des params
//try{

    // Supposons que $array contient votre tableau PHP
    $col3_values = array_column($resultats, 'COL 3');

    // Construire la requête avec des paramètres liés
    $params = array_fill(0, count($col3_values), '?');
    $placeholders = implode(', ', $params);
    
    $requeteTable4 = "SELECT DISTINCT `COL 2`,`COL 3` FROM def_param WHERE `COL 2` IN ($placeholders)";
    $statementTable4 = $pdo->prepare($requeteTable4);
    
    $statementTable4->execute($col3_values);
    $definition_params = $statementTable4->fetchAll(PDO::FETCH_ASSOC);
    
// }

// catch (PDOException $e) {
//     echo "Erreur SQL : " . $e->getMessage();
// }

// Fermeture de la connexion à la base de données
$pdo = null;


// Colonnes à exclure
$colonnesAExclure = ['COL 1', 'COL 2', 'COL 3', 'COL 9'];

// Mapping des anciens noms de colonnes aux nouveaux noms
$mappingColonnes = [
    'COL 4' => 'Résultat alphanumérique',
    'COL 5' => 'Résultat numérique',
    'COL 6' => 'Unité',
    'COL 7' => 'Limite qualité',
    'COL 8' => 'Référence qualité',
    'COL 10' => 'Date de prélèvement',
    'COL 11' => 'Conclusion conformité prélèvement',
    // Ajouter d'autres mappings au besoin
];




// print_r($villes_dep);
$villef = utf8_encode($ville[0]['COL 2']);
$dep = $resultats[0]['COL 2'];
$nb = count($resultats);

?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../style.css">

<?php

echo "<title>Analyse de la qualité de l'eau potable à $villef $code_dep | LeauPotable.fr</title>";

echo "<meta name=\"description\" content=\"Résultat du contrôle sanitaire de la qualité de l'eau potable à $villef $code_dep basé sur $nb paramètres\"> ";

echo "<link rel='canonical' href='https://leaupotable.fr$url'>";

?>

</head>
<body>

<?php
include('header.php');


echo "<h1 class='text-center bleu'>Qualité de l'eau potable à $villef $code_dep</h1>";


echo "<h2 class='text-center m5 bleu'>Analyse de l'eau potable à $villef $code_dep basée sur $nb paramètres</h2>";

echo "<div class='container'>";

// stiky menu start
include('stickyMenu.php');
// stiky menu end

    echo"<h6 class='text-center bleu m-titre'>Vous avez une suggestion ou constaté un bug ?</h6>
    <p>Partagez vos avis et suggestions pour améliorer notre service gratuit en remplissant le formulaire ci-dessous.</p>
    ";
    
    include('formulaire-feedback.php');


//end container div
echo "</div>";

include('footer.php');
?>


<script src="../script.js"></script>
</body>
</html>