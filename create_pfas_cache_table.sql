-- Table pour le cache des résultats PFAS
CREATE TABLE IF NOT EXISTS pfas_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    commune_code VARCHAR(5) NOT NULL,
    commune_name VARCHAR(255) NOT NULL,
    pfas_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    results_count INT DEFAULT 0,
    INDEX idx_commune_code (commune_code),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
