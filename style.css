
body {
    font-family: Arial, sans-serif;
    margin: 0;
}

h1{
  margin-top: 3rem;
}

/* h2{
    margin-bottom: 3rem;
  } */

  h4{
    font-size: 1.25rem;
  }

  h5{
    font-size: 1.15rem;
  }

  h6{
    font-size: 1.1rem;
  }

  .m5{
    margin-bottom: 4rem;
  }

.card {
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: background-color 0.3s ease-in-out;
}

.card-header {
    background-color: #dbe6f7;
    padding: 10px;
    border-bottom: 1px solid #dddddd;
}

.card-body {
    padding: 20px;
}

@media only screen and (max-width: 600px) {
    .card {
        border: none;
    }
}

/* Ajouter des classes pour chaque couleur souhaitée */
.couleur1 { background-color: #90EE90; }
.couleur2 { background-color: #f3bd9e; }
.couleur3 { background-color: #fc5d5d; }
/* ... Ajouter d'autres classes pour d'autres couleurs si nécessaire */

.container {
padding-right: 15px;
padding-left: 15px;
margin-right: auto;
margin-left: auto;
/* display: flex;
flex-direction: row;
align-items: center;
justify-content: center;   */
}
@media (min-width: 768px) {
.container {
width: 750px;
}
}
@media (min-width: 992px) {
.container {
width: 970px;
}
}
@media (min-width: 1200px) {
.container {
width: 1170px;
}
}

.text-center{
text-align: center;
}

.bleu{
  color: #014e8d;
}

.rouge{
  color: #de1414;
}

.m-titre{
  margin-bottom: 2.5rem;
  margin-top: 4rem;
}

/* Style header START */
* {
  box-sizing: border-box;
}


#conteur a{
  color:#fff; text-decoration : none;
}

#conteur2 a{
  text-decoration : none;
}

#conteur2{
  margin: 0.75rem 0 0.75rem 0;
}

.nav {
  height: 50px;
  width: 100%;
  padding: 0 5rem;
  background-color: #0063b2;
  position: relative;
}

.nav > .nav-header {
  display: inline;
  padding-top: 15px;
}

.nav > .nav-header > .nav-title {
  display: inline-block;
  font-size: 22px;
  color: #fff;
  padding: 10px;
}

.nav > .nav-btn {
  display: none;
}

.nav > .nav-links {
  display: inline;
  float: right;
  font-size: 18px;
}

.nav > .nav-links > a {
  display: inline-block;
  padding: 13px 10px 13px 10px;
  text-decoration: none;
  color: #efefef;
}

.nav > .nav-links > a:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.nav > #nav-check {
  display: none;
}

@media (max-width:600px) {
  .nav > .nav-btn {
    display: inline-block;
    position: absolute;
    right: 0px;
    top: 0px;
  }
  .nav > .nav-btn > label {
    display: inline-block;
    width: 50px;
    height: 50px;
    padding: 13px;
  }
  .nav > .nav-btn > label:hover,.nav  #nav-check:checked ~ .nav-btn > label {
    background-color: rgba(0, 0, 0, 0.3);
  }
  .nav > .nav-btn > label > span {
    display: block;
    width: 25px;
    height: 10px;
    border-top: 2px solid #eee;
  }
  .nav > .nav-links {
    position: absolute;
    display: block;
    width: 100%;
    background-color: #cce4f8;
    height: 0px;
    transition: all 0.3s ease-in;
    overflow-y: hidden;
    top: 50px;
    left: 0px;
  }
  .nav > .nav-links > a {
    display: block;
    width: 100%;
    color: #000;
  }
  .nav > #nav-check:not(:checked) ~ .nav-links {
    height: 0px;
  }
  .nav > #nav-check:checked ~ .nav-links {
    height: calc(100vh - 50px);
    overflow-y: auto;
  }
}
  /* Style header END */


/* Style Formulaire contact START */

.container-form {
  width: 100%;
  margin: 50px auto;
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

form {
  display: flex;
  flex-direction: column;
}



input[type="text"],
input[type="email"],
textarea {
  margin-bottom: 10px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

/* input[type="checkbox"]{
  margin:22px 0 15px 0;

} */

.cgu {
  /* margin: 20px; */
  /* vertical-align:middle; */

  /* padding: 10px; */

  width:100%;
  display: flex;
  justify-content: center;
  align-items: center;

}

#acceptCgu{
  width: 10px;
  margin:20px 5px 15px 0;
}

.label1 {
  margin:20px 0 15px 0;
  /* width: 80%; */
}


input[type="submit"] {
  background-color: #74f0a7;
  color: #fff;
  padding: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: 80%;
  display: flex;
  justify-content: center;
  margin: auto;
  font-size: 1rem;
  color: #003250;
  font-weight: 700;
}

input[type="submit"]:hover {
  background-color: #4df191;
}


  /* Style Formulaire contact END */


/* Responsive img */

.responsive {
  max-width: 100%;
  height: auto;
}


/* Footer */

.foot{
  color:  #fff;
  background-color: #0063b2;
  padding: 25px 0 15px 0;
  margin-top: 100px;
 }

 .foot a{
  color:  #fff;
 }

 /* Home STRAT */

 .container-home {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 20px;
}

.column {
  flex: 0 1 calc(33.3333% - 20px); /* Trois colonnes égales, avec un espace entre chaque colonne */
  margin: 10px;
  background-color: #d7ebfc;
  padding: 15px;
  box-sizing: border-box;
}

@media screen and (max-width: 768px) {
  .column {
    flex: 0 1 calc(50% - 20px); /* Deux colonnes sur des écrans plus petits */
  }
}

@media screen and (max-width: 480px) {
  .column {
    flex: 1 100%; /* Une colonne sur des écrans encore plus petits */
  }
}

.link {
  text-decoration: none;
  color: #014e8d;
  display: block;
  margin-bottom: 15px;
  font-weight: 500;
}

/* Home END */

li{
  margin-bottom: 1.5rem;
}


/* stikymenu.php */

.container-stiky {
  display: flex;
  /* height: 100vh; */
  width: 100%;
  flex-direction: row;
  /* align-items: center; */
  justify-content: center;
  flex-wrap: wrap;

}



.left-section {
  width: 35%;
  background-color: #dbe6f7;
  /* position: -webkit-sticky; */
  position: sticky;
  top: 0;
  height: 100%;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.right-section {
  flex-grow: 1;
  padding: 0 20px;
  box-sizing: border-box;
  overflow-y: auto;
  width: 65%;
}


@media (max-width: 800px) {
.container-stiky {
  flex-direction: column;
}

.left-section {
width: 100%;
/* flex-direction: row; */
position: relative;
margin-bottom: 20px;
}

.right-section {
width: 100%;
}
}

.button-st {
  display: block;
  width: 100%;
  padding: 20px;
  margin-bottom: 10px;
  background-color: #DD2C00;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.button-st:hover {
  background-color: #ee471d;
}

/* Styles for the modal */
/* [Object] Modal
* =============================== */
.modal {
opacity: 0;
visibility: hidden;
position: fixed;
top: 0;
right: 0;
bottom: 0;
left: 0;
text-align: left;
background: rgba(0,0,0, .9);
transition: opacity .25s ease;
z-index: 999;
}

.modal__bg {
position: absolute;
top: 0;
right: 0;
bottom: 0;
left: 0;
cursor: pointer;
}

.modal-state {
display: none;
}

.modal-state:checked + .modal {
opacity: 1;
visibility: visible;
}

.modal-state:checked + .modal .modal__inner {
top: 0;
}

.modal__inner {
transition: top .25s ease;
position: absolute;
top: -20%;
right: 0;
bottom: 0;
left: 0;
width: 95%;
margin: auto;
overflow: auto;
background: #fff;
border-radius: 5px;
padding: 1em 2em;
height: 90%;
z-index: 999;
}

.modal__close {
position: absolute;
right: 1em;
top: 1em;
width: 1.1em;
height: 1.1em;
cursor: pointer;
}

.modal__close:after,
.modal__close:before {
content: '';
position: absolute;
width: 2px;
height: 1.5em;
background: #ccc;
display: block;
transform: rotate(45deg);
left: 50%;
margin: -3px 0 0 -1px;
top: 0;
}

.modal__close:hover:after,
.modal__close:hover:before {
background: #aaa;
}

.modal__close:before {
transform: rotate(-45deg);
}

@media screen and (max-width: 768px) {
.modal__inner {
width: 90%;
height: 90%;
box-sizing: border-box;
}

.stickybutton-st {
display: none;
}
}

/* Stikymenu.php END */

/* formDevis.php Start */

.container-devis {
  max-width: 900px;
  margin: auto;
  padding: 10px;
  /* border: 1px solid #343a40; */
  border-radius: 8px;
  text-align: center;
}
.row-devis {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0.25rem;
  align-items: center;
  justify-content: center;
}
.col-md-6-devis, .col-md-12-devis {
  flex: 1;
  padding: 0.5rem;

}
.col-md-6-devis {
  max-width: 50%;

}
.col-md-12-devis {
  max-width: 100%;
}
label {
  display: block;
  /* width: 50%; */
  /* margin-bottom: 0.5rem; */
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  margin: 0 1rem 0 1rem;
}
select{
  width: 60%;
  /* padding: 0.5rem; */
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin: 0 1rem 0 1rem;

}

.input-devis {
  width: 60%;
  /* padding: 0.5rem; */
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin: 0 1rem 0 1rem;

}

.textarea-devis {
  width: 95%;
  /* padding: 0.5rem; */
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin: 0 1rem 0 1rem;

}

.btn-success {
  background-color: #de1414;
  border: none;
  color: #fff;
  font-weight: 900;
  padding: 1rem 3.5rem;
  border-radius: 4px;
  cursor: pointer;
}
.text-center {
  text-align: center;
}
.text-success {
  color: #28a745;
}
.mt-4 {
  margin-top: 1.5rem;
}

.mt-5{
  margin-top: 3.5rem;
}

.my-2 {
  margin: 1rem 0;
}

@media (max-width: 768px) {
  .col-md-6-devis {
      flex: 1 1 100%;
      max-width: 100%;
      margin-bottom: 0.1rem;
      margin-top: 0.1rem;

  }


  .row-devis{
      width: 100%;
      /* margin-bottom: 0.1rem; */
  }



  input, textarea {
  width: 100%;

}

}

/* formDevis.php END  */

/* sticky button START */

.stickyButton {

  display: none; /* Hide the button initially */

}


@media screen and (max-width: 768px) {
  .modal__inner {
    width: 90%;
    height: 90%;
    box-sizing: border-box;
  }

  .stickyButton {
    position: fixed;
    background-color: #de1414;
    color: white;
    font-weight: bold;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 90;
    padding : 7px 0
  }

  .stickyButton.show {
    display: block; /* Show the button when the class 'show' is added */
  }

}

.mar {
  margin-top: 1rem;
  /* margin-bottom: 3rem; */
}


/* sticky button END */

/* verifier-form.php START */


.search-container, .parameter-search-container {
  position: relative;
  width: 100%;
  margin: 0 auto;
}

#search, #parameter-search {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  box-sizing: border-box;
}

@media only screen and (max-width: 768px) {
  .search-container, .parameter-search-container  {
      max-width: 100%;
      /* padding: 0 10px; */
  }

  #search, #parameter-search {
      font-size: 14px;
      padding: 10px;
  }
}

@media only screen and (max-width: 480px) {
  #search, #parameter-search {
      font-size: 12px;
      padding: 8px;
  }
}


      #autocomplete {
          position: absolute;
          width: 100%;
          background-color: white;
          border: 1px solid #ddd;
          max-height: 200px;
          overflow-y: auto;
          display: none;
          z-index: 100;
      }
      .suggestion {
          padding: 10px;
          cursor: pointer;
      }
      .suggestion:hover {
          background-color: #f0f0f0;
      }
      #results {
          margin-top: 20px;
      }
      .result-item {
          padding: 15px;
          margin-bottom: 10px;
          border-radius: 5px;
      }
      .result-item.green {
          background-color: #f5f5f5;  /* Gris neutre clair */
      }
      .result-item.red {
          background-color: #fc5d5d;  /* Rouge clair */
      }
      #loading {
          display: none;
          width: 50px;
          height: 50px;
          border: 5px solid #f3f3f3;
          border-top: 5px solid #01579B;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 20px auto;
      }
      @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
      }
      /* ... (styles précédents) ... */
      .search-button {
          margin-top: 20px;
          padding: 10px 20px;
          font-size: 16px;
          background-color: #01579B;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          width: 70%;
      }
      .search-button:hover {
          background-color: #0277BD;
      }

      .reset-button {
  margin-top: 20px;
  /* margin-left: 10px; */
  padding: 10px 20px;
  font-size: 16px;
  background-color: #DD2C00;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  width: 28%;
}

.reset-button:hover {
  background-color: #ee471d;
}

.toggle-parameter-search {
  cursor: pointer;
  color: #01579B;
}

@media only screen and (max-width: 765px) {
  .reset-button {
      width: 100%;
  }

  .search-button {
      width: 100%;
  }

}

/* Accordeon START */
.accordion {
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-bottom: 10px;
          font-size: 1rem;
      }

      .accordion-header {
          background-color: #f4f4f4;
          padding: 15px;
          cursor: pointer;
          font-weight: bold;
          transition: background-color 0.3s ease;
      }

      .accordion-header:hover {
          background-color: #e4e4e4;
      }

      .accordion-content {
          display: none;
          padding: 15px;
          border-top: 1px solid #ddd;
      }

      .accordion-header::after {
          content: '+';
          float: right;
          font-weight: bold;
      }

      .active::after {
          content: '-';
      }

      .active {
          background-color: #e4e4e4;
      }
/* Accordeon END */

/* verifier-form.php END */