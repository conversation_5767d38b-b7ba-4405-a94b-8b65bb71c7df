<?php


/* <PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON> (Friendly Url) : convertit un titre en une URL conviviale.*/
function slugify($text) {
    // Remplacer les caractères spéciaux accentués
    $search = array('À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'à', 'á', 'â', 'ã', 'ä', 'å', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'È', 'É', 'Ê', 'Ë', 'è', 'é', 'ê', 'ë', 'Ç', 'ç', 'Ì', 'Í', 'Î', 'Ï', 'ì', 'í', 'î', 'ï', 'Ù', 'Ú', 'Û', 'Ü', 'ù', 'ú', 'û', 'ü', 'ÿ', 'Ñ', 'ñ');
    $replace = array('A', 'A', 'A', 'A', 'A', 'A', 'a', 'a', 'a', 'a', 'a', 'a', 'O', 'O', 'O', 'O', 'O', 'O', 'o', 'o', 'o', 'o', 'o', 'o', 'E', 'E', 'E', 'E', 'e', 'e', 'e', 'e', 'C', 'c', 'I', 'I', 'I', 'I', 'i', 'i', 'i', 'i', 'U', 'U', 'U', 'U', 'u', 'u', 'u', 'u', 'y', 'N', 'n');

    $text = str_replace($search, $replace, $text);

    // Convertir en minuscules
    $text = strtolower($text);

    // Remplacer les caractères non alphanumériques par des tirets
    $text = preg_replace('/[^a-z0-9-]+/', '-', $text);

    // Supprimer les tirets au début et à la fin
    $text = trim($text, '-');

    return $text;
}
  
    // Afficher les résultats sous forme de liste HTML
    echo "<ul class='m5'>";
    foreach ($villes_dep as $row) {

        $valeur2 = utf8_encode($row['COL 2']);
        $code_commune = $row['COL 1'];

        $slug = slugify($valeur2);

         echo "<li id='conteur2'>"."<a href='/analyse/qualite-eau-potable-$slug-$code_commune' target='_blank'>". "Qualité de l'eau potable à ". $valeur2." ".$code_dep. "</a></li>";
    }
    echo "</ul>";
    

?>