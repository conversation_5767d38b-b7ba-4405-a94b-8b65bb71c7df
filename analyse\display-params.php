<?php

// Tri des résultats par ordre alphabétique en fonction de 'nouveau_colonne2'
usort($resultats, function ($a, $b) {
    return strcasecmp($a['COL 3'], $b['COL 3']);
});

// Affichage des résultats sous forme de cartes avec utf8_encode et couleur dynamique
foreach ($resultats as $resultat) {

    

    // Obtenez la valeur de la colonne 11
    //$resultatEncoded = utf8_encode($resultat);
    $valeurColonne11 = utf8_encode($resultat['COL 11']);

    //echo $resultat[count($resultat)-1];

    // Choisissez une classe de couleur en fonction de la valeur de la colonne 11
    $classeCouleur = '';
    if ($valeurColonne11 == "Eau d'alimentation conforme aux exigences de qualité en vigueur pour l'ensemble des paramètres mesurés.") {
        $classeCouleur = 'couleur1';
    } elseif ($valeurColonne11 == "Eau d'alimentation non-conforme aux limites de qualité et conforme aux références de qualité.") {
        $classeCouleur = 'couleur2';
    } else {
        $classeCouleur = 'couleur3';
    }

    // Affichage de la carte avec la couleur dynamique
    echo "<div class='card $classeCouleur'>";
    echo "<div class='card-header'>";

    // Renommer la colonne COL1 à code_dep
    // if (isset($resultat['COL 11'])) {
    //     $resultat['Conclusion conformité prélèvement'] = $resultat['COL 11'];
    //     unset($resultat['COL 11']);
    // }

    $titre= utf8_encode($resultat['COL 3']);

    echo "<h3>$titre</h3>";

    echo "</div>";
    echo "<div class='card-body'>";
    foreach ($resultat as $colonne => $valeur) {
        // Renommer les colonnes selon le mapping
        $colonne = isset($mappingColonnes[$colonne]) ? $mappingColonnes[$colonne] : $colonne;


        // Exclure les colonnes spécifiées
        if (!in_array($colonne, $colonnesAExclure)) {
            // Appliquer utf8_encode à chaque valeur
            $valeur = utf8_encode($valeur);
            echo "<p><strong>$colonne:</strong> $valeur</p>";
        }
    }
    echo "</div>";
    echo "</div>";
}

?>