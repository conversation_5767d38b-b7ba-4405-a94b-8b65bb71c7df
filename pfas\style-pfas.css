/* Styles spécifiques pour la page PFAS */

.pfas-info-box {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pfas-info-box h3 {
    color: #1976d2;
    margin-top: 0;
    margin-bottom: 15px;
}

.pfas-search-container {
    margin: 30px 0;
}

.search-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

.search-info p {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Barre de progression */
.loading-container {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50 0%, #2196f3 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

#pfas-status {
    font-weight: 500;
    color: #666;
    margin: 0;
}

/* Résultats PFAS */
.results-container {
    margin: 20px 0;
}

.pfas-result-item {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.pfas-result-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.pfas-result-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.pfas-result-header.conform {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    border-left: 4px solid #4caf50;
}

.pfas-result-header.non-conform {
    background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
    border-left: 4px solid #f44336;
}

.pfas-result-header.sum-pfas {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-left: 4px solid #ff9800;
}

.pfas-result-header.no-data {
    background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
    border-left: 4px solid #9e9e9e;
}

.pfas-result-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.conformity-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.conformity-badge.conform {
    background: #4caf50;
    color: white;
}

.conformity-badge.non-conform {
    background: #f44336;
    color: white;
}

.no-data-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    background: #9e9e9e;
    color: white;
}

/* Éléments "pas de données" */
.pfas-no-data-item {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.pfas-no-data-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.no-data-message {
    padding: 20px;
    text-align: center;
}

.no-data-message p {
    margin-bottom: 10px;
    color: #666;
}

.no-data-explanation {
    font-size: 14px;
    color: #999;
    font-style: italic;
}

.pfas-result-content {
    padding: 20px;
}

.result-main-info {
    margin-bottom: 20px;
}

.result-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.result-date {
    color: #666;
    margin-bottom: 0;
}

.result-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.result-details-complete {
    margin-top: 20px;
}

.result-details-complete h4 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e3f2fd;
    font-size: 16px;
}

.all-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-weight: 500;
    color: #555;
}

.detail-value {
    color: #333;
    text-align: right;
}

.all-details .detail-item {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.all-details .detail-item:last-child {
    border-bottom: none;
}

.all-details .detail-label {
    font-weight: 500;
    color: #555;
    flex: 1;
    margin-right: 15px;
}

.all-details .detail-value {
    flex: 1;
    text-align: right;
    word-break: break-word;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.section-title {
    color: #1976d2;
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 10px 0;
    padding: 8px 12px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 4px;
    border-left: 3px solid #2196f3;
}

.detail-section .detail-item {
    margin-left: 10px;
}

/* Compteur de résultats */
.results-counter-container {
    text-align: center;
    margin: 20px 0;
    padding: 10px;
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    border: 1px solid #4caf50;
    border-radius: 6px;
}

.results-counter {
    margin: 0;
    font-weight: 600;
    color: #2e7d32;
    font-size: 16px;
}

.result-provider-info {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.provider-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 14px;
}

.provider-item:last-child {
    margin-bottom: 0;
}

.provider-label {
    color: #666;
    font-weight: 500;
}

.provider-value {
    color: #333;
    text-align: right;
}

/* Erreurs */
.pfas-error-item {
    background: #ffebee;
    border: 1px solid #f44336;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.pfas-error-header {
    padding: 15px 20px;
    background: #f44336;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pfas-error-header h3 {
    margin: 0;
    font-size: 16px;
}

.error-badge {
    padding: 4px 8px;
    background: rgba(255,255,255,0.2);
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.pfas-error-content {
    padding: 15px 20px;
}

.error-details {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
}

.error-actions {
    margin-top: 15px;
    text-align: center;
}

.retry-button {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
}

.retry-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.4);
}

.retry-button:active {
    transform: translateY(0);
}

/* Éléments de rechargement */
.pfas-retry-loading {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.loading-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    background: #2196f3;
    color: white;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.5;
    }
}

.retry-loading-message {
    padding: 20px;
    text-align: center;
}

.retry-loading-message p {
    margin: 0;
    color: #666;
    font-style: italic;
}

.error-message {
    background: #ffebee;
    border: 1px solid #f44336;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.error-message h3 {
    color: #f44336;
    margin-top: 0;
}

/* Aucun résultat */
.no-results {
    background: #fff3e0;
    border: 1px solid #ff9800;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    margin: 20px 0;
}

.no-results h3 {
    color: #f57c00;
    margin-top: 0;
    margin-bottom: 15px;
}

.no-results ul {
    text-align: left;
    max-width: 600px;
    margin: 20px auto 0;
}

.no-results li {
    margin-bottom: 8px;
    color: #666;
}

/* Informations complémentaires */
.pfas-additional-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 25px;
    margin: 30px 0;
}

.pfas-additional-info h3 {
    color: #333;
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
}

.pfas-compounds {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.compound-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #2196f3;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.compound-item strong {
    color: #1976d2;
    display: block;
    margin-bottom: 5px;
}

.compound-item.special {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-left-color: #ff9800;
}

.compound-item.special strong {
    color: #f57c00;
}

.pfas-intro {
    text-align: center;
    margin-bottom: 20px;
    font-style: italic;
    color: #666;
}

/* Section feedback */
.feedback-section {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #eee;
}

/* Responsive */
@media (max-width: 768px) {
    .pfas-result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .detail-item,
    .provider-item {
        flex-direction: column;
        gap: 5px;
    }

    .detail-value,
    .provider-value {
        text-align: left;
    }

    .pfas-compounds {
        grid-template-columns: 1fr;
    }
}
